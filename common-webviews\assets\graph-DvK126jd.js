import{aq as G,ar as k,as as f,at as O,au as y}from"./AugmentMessage-FtcicXdY.js";import{a as q,c as x,k as _,f as m,d as a,i as l,v,r as z}from"./_baseUniq-BPN2Ft2x.js";var S=G(function(o){return q(x(o,1,k,!0))}),A="\0",d="\0",N="";class E{constructor(t={}){this._isDirected=!Object.prototype.hasOwnProperty.call(t,"directed")||t.directed,this._isMultigraph=!!Object.prototype.hasOwnProperty.call(t,"multigraph")&&t.multigraph,this._isCompound=!!Object.prototype.hasOwnProperty.call(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=f(void 0),this._defaultEdgeLabelFn=f(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[d]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(t){return this._label=t,this}graph(){return this._label}setDefaultNodeLabel(t){return O(t)||(t=f(t)),this._defaultNodeLabelFn=t,this}nodeCount(){return this._nodeCount}nodes(){return _(this._nodes)}sources(){var t=this;return m(this.nodes(),function(e){return y(t._in[e])})}sinks(){var t=this;return m(this.nodes(),function(e){return y(t._out[e])})}setNodes(t,e){var s=arguments,r=this;return a(t,function(i){s.length>1?r.setNode(i,e):r.setNode(i)}),this}setNode(t,e){return Object.prototype.hasOwnProperty.call(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=e),this):(this._nodes[t]=arguments.length>1?e:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]=d,this._children[t]={},this._children[d][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)}node(t){return this._nodes[t]}hasNode(t){return Object.prototype.hasOwnProperty.call(this._nodes,t)}removeNode(t){if(Object.prototype.hasOwnProperty.call(this._nodes,t)){var e=s=>this.removeEdge(this._edgeObjs[s]);delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],a(this.children(t),s=>{this.setParent(s)}),delete this._children[t]),a(_(this._in[t]),e),delete this._in[t],delete this._preds[t],a(_(this._out[t]),e),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this}setParent(t,e){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(l(e))e=d;else{for(var s=e+="";!l(s);s=this.parent(s))if(s===t)throw new Error("Setting "+e+" as parent of "+t+" would create a cycle");this.setNode(e)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=e,this._children[e][t]=!0,this}_removeFromParentsChildList(t){delete this._children[this._parent[t]][t]}parent(t){if(this._isCompound){var e=this._parent[t];if(e!==d)return e}}children(t){if(l(t)&&(t=d),this._isCompound){var e=this._children[t];if(e)return _(e)}else{if(t===d)return this.nodes();if(this.hasNode(t))return[]}}predecessors(t){var e=this._preds[t];if(e)return _(e)}successors(t){var e=this._sucs[t];if(e)return _(e)}neighbors(t){var e=this.predecessors(t);if(e)return S(e,this.successors(t))}isLeaf(t){return(this.isDirected()?this.successors(t):this.neighbors(t)).length===0}filterNodes(t){var e=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});e.setGraph(this.graph());var s=this;a(this._nodes,function(n,h){t(h)&&e.setNode(h,n)}),a(this._edgeObjs,function(n){e.hasNode(n.v)&&e.hasNode(n.w)&&e.setEdge(n,s.edge(n))});var r={};function i(n){var h=s.parent(n);return h===void 0||e.hasNode(h)?(r[n]=h,h):h in r?r[h]:i(h)}return this._isCompound&&a(e.nodes(),function(n){e.setParent(n,i(n))}),e}setDefaultEdgeLabel(t){return O(t)||(t=f(t)),this._defaultEdgeLabelFn=t,this}edgeCount(){return this._edgeCount}edges(){return v(this._edgeObjs)}setPath(t,e){var s=this,r=arguments;return z(t,function(i,n){return r.length>1?s.setEdge(i,n,e):s.setEdge(i,n),n}),this}setEdge(){var t,e,s,r,i=!1,n=arguments[0];typeof n=="object"&&n!==null&&"v"in n?(t=n.v,e=n.w,s=n.name,arguments.length===2&&(r=arguments[1],i=!0)):(t=n,e=arguments[1],s=arguments[3],arguments.length>2&&(r=arguments[2],i=!0)),t=""+t,e=""+e,l(s)||(s=""+s);var h=c(this._isDirected,t,e,s);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,h))return i&&(this._edgeLabels[h]=r),this;if(!l(s)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(e),this._edgeLabels[h]=i?r:this._defaultEdgeLabelFn(t,e,s);var u=function(D,P,F,C){var g=""+P,p=""+F;if(!D&&g>p){var M=g;g=p,p=M}var w={v:g,w:p};return C&&(w.name=C),w}(this._isDirected,t,e,s);return t=u.v,e=u.w,Object.freeze(u),this._edgeObjs[h]=u,L(this._preds[e],t),L(this._sucs[t],e),this._in[e][h]=u,this._out[t][h]=u,this._edgeCount++,this}edge(t,e,s){var r=arguments.length===1?b(this._isDirected,arguments[0]):c(this._isDirected,t,e,s);return this._edgeLabels[r]}hasEdge(t,e,s){var r=arguments.length===1?b(this._isDirected,arguments[0]):c(this._isDirected,t,e,s);return Object.prototype.hasOwnProperty.call(this._edgeLabels,r)}removeEdge(t,e,s){var r=arguments.length===1?b(this._isDirected,arguments[0]):c(this._isDirected,t,e,s),i=this._edgeObjs[r];return i&&(t=i.v,e=i.w,delete this._edgeLabels[r],delete this._edgeObjs[r],j(this._preds[e],t),j(this._sucs[t],e),delete this._in[e][r],delete this._out[t][r],this._edgeCount--),this}inEdges(t,e){var s=this._in[t];if(s){var r=v(s);return e?m(r,function(i){return i.v===e}):r}}outEdges(t,e){var s=this._out[t];if(s){var r=v(s);return e?m(r,function(i){return i.w===e}):r}}nodeEdges(t,e){var s=this.inEdges(t,e);if(s)return s.concat(this.outEdges(t,e))}}function L(o,t){o[t]?o[t]++:o[t]=1}function j(o,t){--o[t]||delete o[t]}function c(o,t,e,s){var r=""+t,i=""+e;if(!o&&r>i){var n=r;r=i,i=n}return r+N+i+N+(l(s)?A:s)}function b(o,t){return c(o,t.v,t.w,t.name)}E.prototype._nodeCount=0,E.prototype._edgeCount=0;export{E as G};
