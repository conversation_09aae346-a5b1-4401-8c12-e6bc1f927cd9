import {
  af as Je,
  c as De,
  _ as F,
  a3 as Le,
  r as Ce,
  n as de,
  p as Ke,
  aI as ye,
  aJ as xe,
  aK as ze,
  aL as ae,
  q as Qe,
  ag as ie,
  aM as Xe,
  m as re,
  s as ke,
  aN as ue,
  aO as ge,
  e as Fe,
  aP as Ye,
  aQ as Ze,
  aR as et,
  ac as Me,
  aS as tt,
  aT as st,
  Q as Te,
  aU as nt,
  aV as ot,
  ae as at,
  aW as N,
  E as it,
  at as pe,
  O as rt,
  L as le,
  aj as lt,
  K as D,
  aC as ct,
  aa as j,
  aX as B,
  aY as dt,
  aZ as ut,
  l as Pe,
  A as He,
  C as m,
  X as gt,
  $ as pt,
  D as ft,
  J as fe,
  a as ht,
  a0 as mt,
  a_ as vt,
  a1 as Rt,
  H as wt,
  t as J,
  P as k,
  b as z,
  I as Oe,
  G as K,
  ay as qt,
  M as bt,
  N as Ee,
  F as he,
  Y as Ie,
  Z as St,
  a7 as Ve,
  a$ as We,
  aF as At,
  ab as Ct
} from "./SpinnerAugment-BY2Lraps.js";
let yt = !1;

function Jt(e, t) {
  return t
}

function Kt(e, t, s, n, g, a = null) {
  var l = e,
    q = {
      flags: t,
      items: new Map,
      first: null
    };
  !(t & ye) || (l = e.appendChild(Je()));
  var p = null,
    R = !1,
    o = Le(() => {
      var w = s();
      return ze(w) ? w : w == null ? [] : xe(w)
    });
  De(() => {
    var w = F(o),
      v = w.length;
    R && v === 0 || (R = v === 0, function (c, r, C, Q, G, i, y) {
      var Re, we, qe, be;
      var S, P, L, x, u, f, O = !!(G & Xe),
        Ne = !!(G & (ue | ge)),
        X = c.length,
        V = r.items,
        je = r.first,
        h = je,
        b = null,
        A = [],
        E = [];
      if (O)
        for (f = 0; f < X; f += 1) x = i(L = c[f], f), (u = V.get(x)) !== void 0 && ((Re = u.a) == null || Re.measure(), (P ? ? (P = new Set)).add(u));
      for (f = 0; f < X; f += 1)
        if (x = i(L = c[f], f), (u = V.get(x)) !== void 0) {
          if (Ne && xt(u, L, f, G), u.e.f & ae && (Ce(u.e), O && ((we = u.a) == null || we.unfix(), (P ? ? (P = new Set)).delete(u))), u !== h) {
            if (S !== void 0 && S.has(u)) {
              if (A.length < E.length) {
                var I, W = E[0];
                b = W.prev;
                var ve = A[0],
                  Y = A[A.length - 1];
                for (I = 0; I < A.length; I += 1) $e(A[I], W, C);
                for (I = 0; I < E.length; I += 1) S.delete(E[I]);
                $(r, ve.prev, Y.next), $(r, b, ve), $(r, Y, W), h = W, b = Y, f -= 1, A = [], E = []
              } else S.delete(u), $e(u, h, C), $(r, u.prev, u.next), $(r, u, b === null ? r.first : b.next), $(r, b, u), b = u;
              continue
            }
            for (A = [], E = []; h !== null && h.k !== x;) h.e.f & ae || (S ? ? (S = new Set)).add(h), E.push(h), h = h.next;
            if (h === null) continue;
            u = h
          }
          A.push(u), b = u, h = u.next
        } else b = kt(h ? h.e.nodes_start : C, r, b, b === null ? r.first : b.next, L, x, f, Q, G, y), V.set(x, b), A = [], E = [], h = b.next;
      if (h !== null || S !== void 0) {
        for (var M = S === void 0 ? [] : xe(S); h !== null;) h.e.f & ae || M.push(h), h = h.next;
        var Z = M.length;
        if (Z > 0) {
          var Be = G & ye && X === 0 ? C : null;
          if (O) {
            for (f = 0; f < Z; f += 1)(qe = M[f].a) == null || qe.measure();
            for (f = 0; f < Z; f += 1)(be = M[f].a) == null || be.fix()
          } (function (T, H, ee, Se) {
            for (var te = [], _ = H.length, se = 0; se < _; se++) Ye(H[se].e, te, !0);
            var ne = _ > 0 && te.length === 0 && ee !== null;
            if (ne) {
              var Ae = ee.parentNode;
              Ze(Ae), Ae.append(ee), Se.clear(), $(T, H[0].prev, H[_ - 1].next)
            }
            et(te, () => {
              for (var oe = 0; oe < _; oe++) {
                var U = H[oe];
                ne || (Se.delete(U.k), $(T, U.prev, U.next)), Me(U.e, !ne)
              }
            })
          })(r, M, Be, V)
        }
      }
      O && Qe(() => {
        var T;
        if (P !== void 0)
          for (u of P) (T = u.a) == null || T.apply()
      }), ie.first = r.first && r.first.e, ie.last = b && b.e
    }(w, q, l, g, t, n, s), a !== null && (v === 0 ? p ? Ce(p) : p = de(() => a(l)) : p !== null && Ke(p, () => {
      p = null
    })), F(o))
  })
}

function xt(e, t, s, n) {
  n & ue && Fe(e.v, t), n & ge ? Fe(e.i, s) : e.i = s
}

function kt(e, t, s, n, g, a, l, q, p, R) {
  var o = p & ue ? p & tt ? ke(g) : re(g, !1, !1) : g,
    w = p & ge ? ke(l) : l,
    v = {
      i: w,
      v: o,
      k: a,
      a: null,
      e: null,
      prev: s,
      next: n
    };
  try {
    return v.e = de(() => q(e, o, w, R), yt), v.e.prev = s && s.e, v.e.next = n && n.e, s === null ? t.first = v : (s.next = v, s.e.next = v.e), n !== null && (n.prev = v, n.e.prev = v.e), v
  } finally { }
}

function $e(e, t, s) {
  for (var n = e.next ? e.next.e.nodes_start : s, g = t ? t.e.nodes_start : s, a = e.e.nodes_start; a !== n;) {
    var l = st(a);
    g.before(a), a = l
  }
}

function $(e, t, s) {
  t === null ? e.first = s : (t.next = s, t.e.next = s && s.e), s !== null && (s.prev = t, s.e.prev = t && t.e)
}

function Qt(e, t, s = !1, n = !1, g = !1) {
  var a = e,
    l = "";
  Te(() => {
    var q = ie;
    if (l !== (l = t() ? ? "") && (q.nodes_start !== null && (nt(q.nodes_start, q.nodes_end), q.nodes_start = q.nodes_end = null), l !== "")) {
      var p = l + "";
      s ? p = `<svg>${p}</svg>` : n && (p = `<math>${p}</math>`);
      var R = ot(p);
      if ((s || n) && (R = N(R)), at(N(R), R.lastChild), s || n)
        for (; N(R);) a.before(N(R));
      else a.before(R)
    }
  })
}

function _e(e, t, ...s) {
  var n, g = e,
    a = pe;
  De(() => {
    a !== (a = t()) && (n && (Me(n), n = null), n = de(() => a(g, ...s)))
  }, it)
}

function Xt(e, t, s) {
  rt(() => {
    var n = le(() => t(e, s == null ? void 0 : s()) || {});
    if (s && (n != null && n.update)) {
      var g = !1,
        a = {};
      lt(() => {
        var l = s();
        D(l), g && ct(a, l) && (a = l, n.update(l))
      }), g = !0
    }
    if (n != null && n.destroy) return () => n.destroy()
  })
}

function d(e, t) {
  var a;
  var s = (a = e.$$events) == null ? void 0 : a[t.type],
    n = ze(s) ? s.slice() : s == null ? [] : [s];
  for (var g of n) g.call(this, t)
}
var Ft = (e => (e.asyncWrapper = "async-wrapper", e.historyLoaded = "history-loaded", e.historyInitialize = "history-initialize", e.completionRating = "completion-rating", e.completionRatingDone = "completion-rating-done", e.nextEditRating = "next-edit-rating", e.nextEditRatingDone = "next-edit-rating-done", e.completions = "completions", e.historyConfig = "history-config", e.copyRequestID = "copy-request-id-to-clipboard", e.openFile = "open-file", e.openDiffInBuffer = "open-diff-in-buffer", e.saveFile = "save-file", e.loadFile = "load-file", e.importFileRequest = "import-file-request", e.importDirectoryRequest = "import-directory-request", e.triggerImportDialogRequest = "trigger-import-dialog-request", e.triggerImportDialogResponse = "trigger-import-dialog-response", e.openMemoriesFile = "open-memories-file", e.openAndEditFile = "open-and-edit-file", e.diffViewNotifyReinit = "diff-view-notify-reinit", e.diffViewLoaded = "diff-view-loaded", e.diffViewInitialize = "diff-view-initialize", e.diffViewResolveChunk = "diff-view-resolve-chunk", e.diffViewFetchPendingStream = "diff-view-fetch-pending-stream", e.diffViewDiffStreamStarted = "diff-view-diff-stream-started", e.diffViewDiffStreamChunk = "diff-view-diff-stream-chunk", e.diffViewDiffStreamEnded = "diff-view-diff-stream-ended", e.diffViewAcceptAllChunks = "diff-view-accept-all-chunks", e.diffViewAcceptFocusedChunk = "diff-view-accept-selected-chunk", e.diffViewRejectFocusedChunk = "diff-view-reject-focused-chunk", e.diffViewFocusPrevChunk = "diff-view-focus-prev-chunk", e.diffViewFocusNextChunk = "diff-view-focus-next-chunk", e.diffViewWindowFocusChange = "diff-view-window-focus-change", e.diffViewFileFocus = "diff-view-file-focus", e.disposeDiffView = "dispose-diff-view", e.reportWebviewClientMetric = "report-webview-client-metric", e.trackAnalyticsEvent = "track-analytics-event", e.reportError = "report-error", e.showNotification = "show-notification", e.showBannerNotification = "show-banner-notification", e.dismissBannerNotification = "dismiss-banner-notification", e.openConfirmationModal = "open-confirmation-modal", e.confirmationModalResponse = "confirmation-modal-response", e.clientTools = "client-tools", e.currentlyOpenFiles = "currently-open-files", e.findFileRequest = "find-file-request", e.resolveFileRequest = "resolve-file-request", e.findFileResponse = "find-file-response", e.resolveFileResponse = "resolve-file-response", e.findRecentlyOpenedFilesRequest = "find-recently-opened-files", e.findRecentlyOpenedFilesResponse = "find-recently-opened-files-response", e.findFolderRequest = "find-folder-request", e.findFolderResponse = "find-folder-response", e.findExternalSourcesRequest = "find-external-sources-request", e.findExternalSourcesResponse = "find-external-sources-response", e.findSymbolRequest = "find-symbol-request", e.findSymbolRegexRequest = "find-symbol-regex-request", e.findSymbolResponse = "find-symbol-response", e.fileRangesSelected = "file-ranges-selected", e.getDiagnosticsRequest = "get-diagnostics-request", e.getDiagnosticsResponse = "get-diagnostics-response", e.resolveWorkspaceFileChunkRequest = "resolve-workspace-file-chunk", e.resolveWorkspaceFileChunkResponse = "resolve-workspace-file-chunk-response", e.sourceFoldersUpdated = "source-folders-updated", e.sourceFoldersSyncStatus = "source-folders-sync-status", e.syncEnabledState = "sync-enabled-state", e.shouldShowSummary = "should-show-summary", e.showAugmentPanel = "show-augment-panel", e.updateGuidelinesState = "update-guidelines-state", e.openGuidelines = "open-guidelines", e.updateWorkspaceGuidelines = "update-workspace-guidelines", e.updateUserGuidelines = "update-user-guidelines", e.chatAgentEditListHasUpdates = "chat-agent-edit-list-has-updates", e.chatMemoryHasUpdates = "chat-memory-has-updates", e.getAgentEditContentsByRequestId = "getAgentEditContentsByRequestId", e.chatModeChanged = "chat-mode-changed", e.chatClearMetadata = "chat-clear-metadata", e.chatLoaded = "chat-loaded", e.chatInitialize = "chat-initialize", e.chatGetStreamRequest = "chat-get-stream-request", e.chatUserMessage = "chat-user-message", e.generateCommitMessage = "generate-commit-message", e.chatUserCancel = "chat-user-cancel", e.chatModelReply = "chat-model-reply", e.chatInstructionMessage = "chat-instruction-message", e.chatInstructionModelReply = "chat-instruction-model-reply", e.chatCreateFile = "chat-create-file", e.chatSmartPaste = "chat-smart-paste", e.chatRating = "chat-rating", e.chatRatingDone = "chat-rating-done", e.chatStreamDone = "chat-stream-done", e.runSlashCommand = "run-slash-command", e.callTool = "call-tool", e.callToolResponse = "call-tool-response", e.cancelToolRun = "cancel-tool-run", e.cancelToolRunResponse = "cancel-tool-run-response", e.toolCheckSafe = "check-safe", e.toolCheckSafeResponse = "check-safe-response", e.checkToolExists = "checkToolExists", e.checkToolExistsResponse = "checkToolExistsResponse", e.startRemoteMCPAuth = "start-remote-mcp-auth", e.getToolCallCheckpoint = "get-tool-call-checkpoint", e.getToolCallCheckpointResponse = "get-tool-call-checkpoint-response", e.updateAditionalChatModels = "update-additional-chat-models", e.saveChat = "save-chat", e.saveChatDone = "save-chat-done", e.newThread = "new-thread", e.chatSaveImageRequest = "chat-save-image-request", e.chatSaveImageResponse = "chat-save-image-response", e.chatLoadImageRequest = "chat-load-image-request", e.chatLoadImageResponse = "chat-load-image-response", e.chatDeleteImageRequest = "chat-delete-image-request", e.chatDeleteImageResponse = "chat-delete-image-response", e.chatSaveAttachmentRequest = "chat-save-attachment-request", e.chatSaveAttachmentResponse = "chat-save-attachment-response", e.instructions = "instructions", e.nextEditDismiss = "next-edit-dismiss", e.nextEditLoaded = "next-edit-loaded", e.nextEditSuggestions = "next-edit-suggestions", e.nextEditSuggestionsAction = "next-edit-suggestions-action", e.nextEditRefreshStarted = "next-edit-refresh-started", e.nextEditRefreshFinished = "next-edit-refresh-finished", e.nextEditCancel = "next-edit-cancel", e.nextEditPreviewActive = "next-edit-preview-active", e.nextEditSuggestionsChanged = "next-edit-suggestions-changed", e.nextEditNextSuggestionChanged = "next-edit-next-suggestion-changed", e.nextEditOpenSuggestion = "next-edit-open-suggestion", e.nextEditToggleSuggestionTree = "next-edit-toggle-suggestion-tree", e.nextEditActiveSuggestionChanged = "next-edit-active-suggestion", e.nextEditPanelFocus = "next-edit-panel-focus", e.onboardingLoaded = "onboarding-loaded", e.onboardingUpdateState = "onboarding-update-state", e.usedChat = "used-chat", e.preferencePanelLoaded = "preference-panel-loaded", e.preferenceInit = "preference-init", e.preferenceResultMessage = "preference-result-message", e.preferenceNotify = "preference-notify", e.openSettingsPage = "open-settings-page", e.settingsPanelLoaded = "settings-panel-loaded", e.navigateToSettingsSection = "navigate-to-settings-section", e.mainPanelDisplayApp = "main-panel-display-app", e.mainPanelLoaded = "main-panel-loaded", e.mainPanelActions = "main-panel-actions", e.mainPanelPerformAction = "main-panel-perform-action", e.mainPanelCreateProject = "main-panel-create-project", e.usedSlashAction = "used-slash-action", e.signInLoaded = "sign-in-loaded", e.signInLoadedResponse = "sign-in-loaded-response", e.signOut = "sign-out", e.awaitingSyncingPermissionLoaded = "awaiting-syncing-permission-loaded", e.awaitingSyncingPermissionInitialize = "awaiting-syncing-permission-initialize", e.readFileRequest = "read-file-request", e.readFileResponse = "read-file-response", e.wsContextGetChildrenRequest = "ws-context-get-children-request", e.wsContextGetChildrenResponse = "ws-context-get-children-response", e.wsContextGetSourceFoldersRequest = "ws-context-get-source-folders-request", e.wsContextGetSourceFoldersResponse = "ws-context-get-source-folders-response", e.wsContextAddMoreSourceFolders = "ws-context-add-more-source-folders", e.wsContextRemoveSourceFolder = "ws-context-remove-source-folder", e.wsContextSourceFoldersChanged = "ws-context-source-folders-changed", e.wsContextFolderContentsChanged = "ws-context-folder-contents-changed", e.wsContextUserRequestedRefresh = "ws-context-user-requested-refresh", e.augmentLink = "augment-link", e.resetAgentOnboarding = "reset-agent-onboarding", e.empty = "empty", e.chatGetAgentOnboardingPromptRequest = "chat-get-agent-onboarding-prompt-request", e.chatGetAgentOnboardingPromptResponse = "chat-get-agent-onboarding-prompt-response", e.getWorkspaceInfoRequest = "get-workspace-info-request", e.getWorkspaceInfoResponse = "get-workspace-info-response", e.getRemoteAgentOverviewsRequest = "get-remote-agent-overviews-request", e.getRemoteAgentOverviewsResponse = "get-remote-agent-overviews-response", e.remoteAgentOverviewsStreamRequest = "remote-agent-overviews-stream-request", e.remoteAgentOverviewsStreamResponse = "remote-agent-overviews-stream-response", e.getRemoteAgentChatHistoryRequest = "get-remote-agent-chat-history-request", e.getRemoteAgentChatHistoryResponse = "get-remote-agent-chat-history-response", e.remoteAgentHistoryStreamRequest = "remote-agent-history-stream-request", e.remoteAgentHistoryStreamResponse = "remote-agent-history-stream-response", e.cancelRemoteAgentsStreamRequest = "cancel-remote-agents-stream-request", e.createRemoteAgentRequest = "create-remote-agent-request", e.createRemoteAgentResponse = "create-remote-agent-response", e.deleteRemoteAgentRequest = "delete-remote-agent-request", e.deleteRemoteAgentResponse = "delete-remote-agent-response", e.remoteAgentChatRequest = "remote-agent-chat-request", e.remoteAgentChatResponse = "remote-agent-chat-response", e.remoteAgentInterruptRequest = "remote-agent-interrupt-request", e.remoteAgentInterruptResponse = "remote-agent-interrupt-response", e.listSetupScriptsRequest = "list-setup-scripts-request", e.listSetupScriptsResponse = "list-setup-scripts-response", e.saveSetupScriptRequest = "save-setup-script-request", e.saveSetupScriptResponse = "save-setup-script-response", e.deleteSetupScriptRequest = "delete-setup-script-request", e.deleteSetupScriptResponse = "delete-setup-script-response", e.renameSetupScriptRequest = "rename-setup-script-request", e.renameSetupScriptResponse = "rename-setup-script-response", e.remoteAgentSshRequest = "remote-agent-ssh-request", e.remoteAgentSshResponse = "remote-agent-ssh-response", e.setRemoteAgentNotificationEnabled = "set-remote-agent-notification-enabled", e.getRemoteAgentNotificationEnabledRequest = "get-remote-agent-notification-enabled-request", e.getRemoteAgentNotificationEnabledResponse = "get-remote-agent-notification-enabled-response", e.deleteRemoteAgentNotificationEnabled = "delete-remote-agent-notification-enabled", e.setRemoteAgentPinnedStatus = "set-remote-agent-pinned-status", e.getRemoteAgentPinnedStatusRequest = "get-remote-agent-pinned-status-request", e.getRemoteAgentPinnedStatusResponse = "get-remote-agent-pinned-status-response", e.deleteRemoteAgentPinnedStatus = "delete-remote-agent-pinned-status", e.remoteAgentNotifyReady = "remote-agent-notify-ready", e.remoteAgentSelectAgentId = "remote-agent-select-agent-id", e.remoteAgentWorkspaceLogsRequest = "remote-agent-workspace-logs-request", e.remoteAgentWorkspaceLogsResponse = "remote-agent-workspace-logs-response", e.remoteAgentPauseRequest = "remote-agent-pause-request", e.remoteAgentResumeRequest = "remote-agent-resume-request", e.remoteAgentResumeHintRequest = "remote-agent-resume-hint-request", e.updateRemoteAgentRequest = "update-remote-agent-request", e.updateRemoteAgentResponse = "update-remote-agent-response", e.updateSharedWebviewState = "update-shared-webview-state", e.getSharedWebviewState = "get-shared-webview-state", e.getSharedWebviewStateResponse = "get-shared-webview-state-response", e.getGitBranchesRequest = "get-git-branches-request", e.getGitBranchesResponse = "get-git-branches-response", e.gitFetchRequest = "git-fetch-request", e.gitFetchResponse = "git-fetch-response", e.isGitRepositoryRequest = "is-git-repository-request", e.isGitRepositoryResponse = "is-git-repository-response", e.getWorkspaceDiffRequest = "get-workspace-diff-request", e.getWorkspaceDiffResponse = "get-workspace-diff-response", e.getRemoteUrlRequest = "get-remote-url-request", e.getRemoteUrlResponse = "get-remote-url-response", e.diffExplanationRequest = "get-diff-explanation-request", e.diffExplanationResponse = "get-diff-explanation-response", e.diffGroupChangesRequest = "get-diff-group-changes-request", e.diffGroupChangesResponse = "get-diff-group-changes-response", e.diffDescriptionsRequest = "get-diff-descriptions-request", e.diffDescriptionsResponse = "get-diff-descriptions-response", e.canApplyChangesRequest = "can-apply-changes-request", e.canApplyChangesResponse = "can-apply-changes-response", e.applyChangesRequest = "apply-changes-request", e.applyChangesResponse = "apply-changes-response", e.previewApplyChangesRequest = "preview-apply-changes-request", e.previewApplyChangesResponse = "preview-apply-changes-response", e.openFileRequest = "open-file-request", e.openFileResponse = "open-file-response", e.stashUnstagedChangesRequest = "stash-unstaged-changes-request", e.stashUnstagedChangesResponse = "stash-unstaged-changes-response", e.isGithubAuthenticatedRequest = "is-github-authenticated-request", e.isGithubAuthenticatedResponse = "is-github-authenticated-response", e.authenticateGithubRequest = "authenticate-github-request", e.authenticateGithubResponse = "authenticate-github-response", e.revokeGithubAccessRequest = "revoke-github-access-request", e.revokeGithubAccessResponse = "revoke-github-access-response", e.listGithubReposForAuthenticatedUserRequest = "list-github-repos-for-authenticated-user-request", e.listGithubReposForAuthenticatedUserResponse = "list-github-repos-for-authenticated-user-response", e.listGithubRepoBranchesRequest = "list-github-repo-branches-request", e.listGithubRepoBranchesResponse = "list-github-repo-branches-response", e.getGithubRepoRequest = "get-github-repo-request", e.getGithubRepoResponse = "get-github-repo-response", e.getCurrentLocalBranchRequest = "get-current-local-branch-request", e.getCurrentLocalBranchResponse = "get-current-local-branch-response", e.remoteAgentDiffPanelLoaded = "remote-agent-diff-panel-loaded", e.remoteAgentDiffPanelSetOpts = "remote-agent-diff-panel-set-opts", e.showRemoteAgentDiffPanel = "show-remote-agent-diff-panel", e.closeRemoteAgentDiffPanel = "close-remote-agent-diff-panel", e.remoteAgentHomePanelLoaded = "remote-agent-home-panel-loaded", e.showRemoteAgentHomePanel = "show-remote-agent-home-panel", e.closeRemoteAgentHomePanel = "close-remote-agent-home-panel", e.triggerInitialOrientation = "trigger-initial-orientation", e.executeInitialOrientation = "execute-initial-orientation", e.orientationStatusUpdate = "orientation-status-update", e.getOrientationStatus = "get-orientation-status", e.checkAgentAutoModeApproval = "check-agent-auto-mode-approval", e.checkAgentAutoModeApprovalResponse = "check-agent-auto-mode-approval-response", e.setAgentAutoModeApproved = "set-agent-auto-mode-approved", e.toolConfigLoaded = "tool-config-loaded", e.toolConfigInitialize = "tool-config-initialize", e.toolConfigSave = "tool-config-save", e.toolConfigGetDefinitions = "tool-config-get-definitions", e.toolConfigDefinitionsResponse = "tool-config-definitions-response", e.toolConfigStartOAuth = "tool-config-start-oauth", e.toolConfigStartOAuthResponse = "tool-config-start-oauth-response", e.toolConfigRevokeAccess = "tool-config-revoke-access", e.toolApprovalConfigSetRequest = "tool-approval-config-set-request", e.toolApprovalConfigGetRequest = "tool-approval-config-get-request", e.toolApprovalConfigGetResponse = "tool-approval-config-get-response", e.getStoredMCPServers = "get-stored-mcp-servers", e.setStoredMCPServers = "set-stored-mcp-servers", e.getStoredMCPServersResponse = "get-stored-mcp-servers-response", e.deleteOAuthSession = "delete-oauth-session", e.getChatRequestIdeStateRequest = "get-ide-state-node-request", e.getChatRequestIdeStateResponse = "get-ide-state-node-response", e.executeCommand = "execute-command", e.toggleCollapseUnchangedRegions = "toggle-collapse-unchanged-regions", e.openScratchFileRequest = "open-scratch-file-request", e.getTerminalSettings = "get-terminal-settings", e.terminalSettingsResponse = "terminal-settings-response", e.updateTerminalSettings = "update-terminal-settings", e.canShowTerminal = "can-show-terminal", e.canShowTerminalResponse = "can-show-terminal-response", e.showTerminal = "show-terminal", e.showTerminalResponse = "show-terminal-response", e.getRemoteAgentStatus = "get-remote-agent-status", e.remoteAgentStatusResponse = "remote-agent-status-response", e.remoteAgentStatusChanged = "remote-agent-status-changed", e.saveLastRemoteAgentSetupRequest = "save-last-remote-agent-setup-request", e.getLastRemoteAgentSetupRequest = "get-last-remote-agent-setup-request", e.getLastRemoteAgentSetupResponse = "get-last-remote-agent-setup-response", e.rulesLoaded = "rules-loaded", e.memoriesLoaded = "memories-loaded", e.getRulesListResponse = "get-rules-list-response", e.getSubscriptionInfo = "get-subscription-info", e.getSubscriptionInfoResponse = "get-subscription-info-response", e.reportRemoteAgentEvent = "report-remote-agent-event", e.reportAgentChangesApplied = "report-agent-changes-applied", e.setPermissionToWriteToSSHConfig = "set-permission-to-write-to-ssh-config", e.getShouldShowSSHConfigPermissionPromptRequest = "get-should-show-ssh-config-permission-prompt-request", e.getShouldShowSSHConfigPermissionPromptResponse = "get-should-show-ssh-config-permission-prompt-response", e))(Ft || {}),
  Pt = (e => (e.off = "off", e.visibleHover = "visible-hover", e.visible = "visible", e.on = "on", e))(Pt || {}),
  Et = (e => (e.accept = "accept", e.reject = "reject", e))(Et || {}),
  It = (e => (e.loading = "loading", e.signIn = "sign-in", e.chat = "chat", e.workspaceContext = "workspace-context", e.awaitingSyncingPermission = "awaiting-syncing-permission", e.folderSelection = "folder-selection", e))(It || {}),
  $t = (e => (e.idle = "idle", e.inProgress = "in-progress", e.succeeded = "succeeded", e.failed = "failed", e.aborted = "aborted", e))($t || {}),
  Gt = (e => (e.included = "included", e.excluded = "excluded", e.partial = "partial", e))(Gt || {}),
  Dt = (e => (e[e.unspecified = 0] = "unspecified", e[e.typingMessage = 1] = "typingMessage", e[e.viewingAgent = 2] = "viewingAgent", e))(Dt || {}),
  me = (e => (e.vscode = "vscode", e.jetbrains = "jetbrains", e.web = "web", e))(me || {});
const ce = "data-vscode-theme-kind";

function Lt() {
  return self.acquireVsCodeApi !== void 0
}

function Ge() {
  dt(function () {
    const e = document.body.getAttribute(ce);
    if (e) return Mt[e]
  }()), ut(function () {
    const e = document.body.getAttribute(ce);
    if (e) return Tt[e]
  }())
}

function zt() {
  if (self.acquireVsCodeApi === void 0) throw new Error("acquireVsCodeAPI not available");
  return function () {
    new MutationObserver(Ge).observe(document.body, {
      attributeFilter: [ce],
      attributes: !0
    }), Ge()
  }(), {
    ...self.acquireVsCodeApi(),
    clientType: me.vscode
  }
}
const Mt = {
  "vscode-dark": j.dark,
  "vscode-high-contrast": j.dark,
  "vscode-light": j.light,
  "vscode-high-contrast-light": j.light
},
  Tt = {
    "vscode-dark": B.regular,
    "vscode-light": B.regular,
    "vscode-high-contrast": B.highContrast,
    "vscode-high-contrast-light": B.highContrast
  };

function Ht() {
  return window.augment_intellij !== void 0
}

function Ot() {
  var e;
  if (Lt()) return zt();
  if (Ht()) return function () {
    const t = window.augment_intellij;
    if (t === void 0 || t.setState === void 0 || t.getState === void 0 || t.postMessage === void 0) throw new Error("Augment IntelliJ host not available");
    window.augment = window.augment || {};
    let s = !1;
    return window.augment.host = {
      clientType: me.jetbrains,
      setState: n => {
        s || console.error("Host not initialized"), t.setState(n)
      },
      getState: () => (s || console.error("Host not initialized"), t.getState()),
      postMessage: n => {
        s || console.error("Host not initialized"), t.postMessage(n)
      },
      initialize: async () => {
        await t.initializationPromise, s = !0
      }
    }, window.augment.host
  }();
  if (!((e = window.augment) != null && e.host)) throw new Error("Augment host not available");
  return window.augment.host
}

function Vt() {
  var e;
  return (e = window.augment) != null && e.host || (window.augment = window.augment || {}, window.augment.host = Ot()), window.augment.host
}
const Yt = Vt();

function Ue(e) {
  return String(e).replace(".", "_")
}
var Wt = fe('<div class="c-base-btn__loading svelte-5auyf2"><!></div> <span class="c-base-btn__hidden-content svelte-5auyf2"><!></span>', 1),
  _t = fe("<button><!></button>");

function Ut(e, t) {
  const s = Pe(t, ["children", "$$slots", "$$events", "$$legacy"]),
    n = Pe(s, ["size", "variant", "color", "disabled", "highContrast", "loading", "alignment", "radius"]);
  He(t, !1);
  const g = re(),
    a = re();
  let l = m(t, "size", 8, 2),
    q = m(t, "variant", 8, "solid"),
    p = m(t, "color", 8, "accent"),
    R = m(t, "disabled", 8, !1),
    o = m(t, "highContrast", 8, !1),
    w = m(t, "loading", 8, !1),
    v = m(t, "alignment", 8, "center"),
    c = m(t, "radius", 8, "medium");
  gt(() => (F(g), F(a), D(n)), () => {
    Ie(g, n.class), Ie(a, St(n, ["class"]))
  }), pt(), ft();
  var r = _t();
  ht(r, (i, y, S, P) => ({
    ...i,
    ...y,
    class: S,
    disabled: R() || w(),
    ...F(a),
    [Rt]: P
  }), [() => mt(p()), () => vt(c()), () => (D(l()), D(q()), D(p()), F(g), D(v()), le(() => `c-base-btn c-base-btn--size-${Ue(l())} c-base-btn--${q()} c-base-btn--${p()} ${F(g)} c-base-btn--alignment-${v()}`)), () => ({
    "c-base-btn--highContrast": o(),
    "c-base-btn--loading": w()
  })], "svelte-5auyf2");
  var C = J(r),
    Q = i => {
      var y = Wt(),
        S = K(y),
        P = J(S);
      const L = Le(() => (D(l()), le(() => function (f) {
        switch (f) {
          case 0:
          case .5:
          case 1:
            return 1;
          case 2:
          case 3:
            return 2;
          case 4:
            return 3
        }
      }(l()))));
      qt(P, {
        get size() {
          return F(L)
        }
      });
      var x = bt(S, 2),
        u = J(x);
      Ee(u, t, "default", {}, null), z(i, y)
    },
    G = i => {
      var y = he(),
        S = K(y);
      Ee(S, t, "default", {}, null), z(i, y)
    };
  wt(C, i => {
    w() ? i(Q) : i(G, !1)
  }), k("click", r, function (i) {
    d.call(this, t, i)
  }), k("keyup", r, function (i) {
    d.call(this, t, i)
  }), k("keydown", r, function (i) {
    d.call(this, t, i)
  }), k("mousedown", r, function (i) {
    d.call(this, t, i)
  }), k("mouseover", r, function (i) {
    d.call(this, t, i)
  }), k("focus", r, function (i) {
    d.call(this, t, i)
  }), k("mouseleave", r, function (i) {
    d.call(this, t, i)
  }), k("blur", r, function (i) {
    d.call(this, t, i)
  }), k("contextmenu", r, function (i) {
    d.call(this, t, i)
  }), z(e, r), Oe()
}
var Nt = fe("<div><!></div>");

function jt(e, t) {
  He(t, !0);
  let s = m(t, "size", 3, 2),
    n = m(t, "variant", 3, "solid"),
    g = m(t, "color", 3, "accent"),
    a = m(t, "highContrast", 3, !1),
    l = m(t, "disabled", 3, !1),
    q = m(t, "radius", 3, "medium"),
    p = m(t, "loading", 3, !1),
    R = We(t, ["$$slots", "$$events", "$$legacy", "size", "variant", "color", "highContrast", "disabled", "radius", "class", "loading", "children"]);
  var o = Nt(),
    w = J(o);
  const v = At(() => l() || void 0);
  Ut(w, Ve({
    get size() {
      return s()
    },
    get variant() {
      return n()
    },
    get color() {
      return g()
    },
    get highContrast() {
      return a()
    },
    get disabled() {
      return F(v)
    },
    get radius() {
      return q()
    },
    get loading() {
      return p()
    },
    get class() {
      return t.class
    }
  }, () => R, {
    $$events: {
      click(c) {
        d.call(this, t, c)
      },
      keyup(c) {
        d.call(this, t, c)
      },
      keydown(c) {
        d.call(this, t, c)
      },
      mousedown(c) {
        d.call(this, t, c)
      },
      mouseover(c) {
        d.call(this, t, c)
      },
      focus(c) {
        d.call(this, t, c)
      },
      mouseleave(c) {
        d.call(this, t, c)
      },
      blur(c) {
        d.call(this, t, c)
      },
      contextmenu(c) {
        d.call(this, t, c)
      }
    },
    children: (c, r) => {
      var C = he();
      _e(K(C), () => t.children ? ? pe), z(c, C)
    },
    $$slots: {
      default: !0
    }
  })), Te(c => Ct(o, 1, c, "svelte-1mz435m"), [() => `c-icon-btn c-icon-btn--size-${Ue(s())}`]), z(e, o), Oe()
}

function Zt(e, t) {
  let s = m(t, "size", 3, 2),
    n = m(t, "variant", 3, "solid"),
    g = m(t, "color", 3, "neutral"),
    a = m(t, "highContrast", 3, !1),
    l = m(t, "disabled", 3, !1),
    q = m(t, "radius", 3, "medium"),
    p = m(t, "loading", 3, !1),
    R = We(t, ["$$slots", "$$events", "$$legacy", "size", "variant", "color", "highContrast", "disabled", "radius", "loading", "children"]);
  jt(e, Ve({
    get size() {
      return s()
    },
    get variant() {
      return n()
    },
    get color() {
      return g()
    },
    get highContrast() {
      return a()
    },
    get disabled() {
      return l()
    },
    get radius() {
      return q()
    },
    get loading() {
      return p()
    }
  }, () => R, {
    $$events: {
      click(o) {
        d.call(this, t, o)
      },
      keyup(o) {
        d.call(this, t, o)
      },
      keydown(o) {
        d.call(this, t, o)
      },
      mousedown(o) {
        d.call(this, t, o)
      },
      mouseover(o) {
        d.call(this, t, o)
      },
      focus(o) {
        d.call(this, t, o)
      },
      mouseleave(o) {
        d.call(this, t, o)
      },
      blur(o) {
        d.call(this, t, o)
      },
      contextmenu(o) {
        d.call(this, t, o)
      }
    },
    children: (o, w) => {
      var v = he();
      _e(K(v), () => t.children ? ? pe), z(o, v)
    },
    $$slots: {
      default: !0
    }
  }))
}
export {
  Ut as B, jt as C, Et as D, me as H, Zt as I, It as M, $t as O, Dt as R, Pt as S, Ft as W, Xt as a, d as b, Yt as c, Gt as d, Kt as e, Lt as f, Vt as g, Qt as h, Jt as i, Ht as j
};