var un=Object.defineProperty;var ln=(n,t,e)=>t in n?un(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var Ae=(n,t,e)=>ln(n,typeof t!="symbol"?t+"":t,e);import{A as Wt,C as H,D as zt,J as C,M as S,t as h,Q as dt,W as mt,b as M,H as X,_ as i,a3 as U,L as w,K as P,I as At,a6 as Ze,m as D,Y as _,X as B,$ as de,am as Re,F as Ke,G as Dt,N as dn,T as mn,V as ke,ab as Ne,aE as ht,P as Pt,al as Ve,aF as Ee,az as hn}from"./SpinnerAugment-BY2Lraps.js";import"./design-system-init-DkEuonq_.js";import{e as Rt,i as re,c as Et,W as gt,B as gn,I as Oe}from"./IconButtonAugment-B8y0FMb_.js";import{C as fn,F as at}from"./feedback-rating-BX5Icwas.js";import{B as _e}from"./ButtonAugment-BoJU5mQc.js";import{t as kt,d as pn,m as tn,a as Fe,b as je}from"./differenceInCalendarDays-CwIWhZCr.js";import{c as vn,S as me,M as bn}from"./index-BPm23rLE.js";import{e as wn,R as Zt}from"./toggleHighContrast-Cb9MCs64.js";import{C as ve}from"./next-edit-types-904A5ehg.js";import{T as yn}from"./TextAreaAugment-BkN7aH6v.js";import{C as xn}from"./CardAugment-BaFOe6RO.js";import{o as Ie}from"./keypress-DD1aQVr0.js";import{M as Ye}from"./MaterialIcon-CKuUXxrb.js";import"./copy-DdR1jezc.js";import"./index-CoHT-xzg.js";import"./preload-helper-Dv6uf1Os.js";import"./BaseTextInput-C64uUToe.js";function Ot(n,t){return n instanceof Date?new n.constructor(t):new Date(t)}let kn={};function ge(){return kn}function ie(n,t){var d,o,c,u;const e=ge(),r=(t==null?void 0:t.weekStartsOn)??((o=(d=t==null?void 0:t.locale)==null?void 0:d.options)==null?void 0:o.weekStartsOn)??e.weekStartsOn??((u=(c=e.locale)==null?void 0:c.options)==null?void 0:u.weekStartsOn)??0,a=kt(n),s=a.getDay(),y=(s<r?7:0)+s-r;return a.setDate(a.getDate()-y),a.setHours(0,0,0,0),a}function he(n){return ie(n,{weekStartsOn:1})}function en(n){const t=kt(n),e=t.getFullYear(),r=Ot(n,0);r.setFullYear(e+1,0,4),r.setHours(0,0,0,0);const a=he(r),s=Ot(n,0);s.setFullYear(e,0,4),s.setHours(0,0,0,0);const y=he(s);return t.getTime()>=a.getTime()?e+1:t.getTime()>=y.getTime()?e:e-1}function Nn(n){if(t=n,!(t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"||typeof n=="number"))return!1;var t;const e=kt(n);return!isNaN(Number(e))}const _n={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function be(n){return(t={})=>{const e=t.width?String(t.width):n.defaultWidth;return n.formats[e]||n.formats[n.defaultWidth]}}const Mn={date:be({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:be({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:be({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Tn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function Kt(n){return(t,e)=>{let r;if((e!=null&&e.context?String(e.context):"standalone")==="formatting"&&n.formattingValues){const a=n.defaultFormattingWidth||n.defaultWidth,s=e!=null&&e.width?String(e.width):a;r=n.formattingValues[s]||n.formattingValues[a]}else{const a=n.defaultWidth,s=e!=null&&e.width?String(e.width):n.defaultWidth;r=n.values[s]||n.values[a]}return r[n.argumentCallback?n.argumentCallback(t):t]}}const Pn={ordinalNumber:(n,t)=>{const e=Number(n),r=e%100;if(r>20||r<10)switch(r%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},era:Kt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:Kt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:n=>n-1}),month:Kt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:Kt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:Kt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function Vt(n){return(t,e={})=>{const r=e.width,a=r&&n.matchPatterns[r]||n.matchPatterns[n.defaultMatchWidth],s=t.match(a);if(!s)return null;const y=s[0],d=r&&n.parsePatterns[r]||n.parsePatterns[n.defaultParseWidth],o=Array.isArray(d)?function(u,f){for(let x=0;x<u.length;x++)if(f(u[x]))return x}(d,u=>u.test(y)):function(u,f){for(const x in u)if(Object.prototype.hasOwnProperty.call(u,x)&&f(u[x]))return x}(d,u=>u.test(y));let c;return c=n.valueCallback?n.valueCallback(o):o,c=e.valueCallback?e.valueCallback(c):c,{value:c,rest:t.slice(y.length)}}}const Dn={ordinalNumber:(te={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:n=>parseInt(n,10)},(n,t={})=>{const e=n.match(te.matchPattern);if(!e)return null;const r=e[0],a=n.match(te.parsePattern);if(!a)return null;let s=te.valueCallback?te.valueCallback(a[0]):a[0];return s=t.valueCallback?t.valueCallback(s):s,{value:s,rest:n.slice(r.length)}}),era:Vt({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Vt({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:n=>n+1}),month:Vt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Vt({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Vt({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var te;const Cn={code:"en-US",formatDistance:(n,t,e)=>{let r;const a=_n[n];return r=typeof a=="string"?a:t===1?a.one:a.other.replace("{{count}}",t.toString()),e!=null&&e.addSuffix?e.comparison&&e.comparison>0?"in "+r:r+" ago":r},formatLong:Mn,formatRelative:(n,t,e,r)=>Tn[n],localize:Pn,match:Dn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Sn(n){const t=kt(n);return pn(t,function(r){const a=kt(r),s=Ot(r,0);return s.setFullYear(a.getFullYear(),0,1),s.setHours(0,0,0,0),s}(t))+1}function qn(n){const t=kt(n),e=+he(t)-+function(r){const a=en(r),s=Ot(r,0);return s.setFullYear(a,0,4),s.setHours(0,0,0,0),he(s)}(t);return Math.round(e/tn)+1}function nn(n,t){var u,f,x,b;const e=kt(n),r=e.getFullYear(),a=ge(),s=(t==null?void 0:t.firstWeekContainsDate)??((f=(u=t==null?void 0:t.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??a.firstWeekContainsDate??((b=(x=a.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,y=Ot(n,0);y.setFullYear(r+1,0,s),y.setHours(0,0,0,0);const d=ie(y,t),o=Ot(n,0);o.setFullYear(r,0,s),o.setHours(0,0,0,0);const c=ie(o,t);return e.getTime()>=d.getTime()?r+1:e.getTime()>=c.getTime()?r:r-1}function $n(n,t){const e=kt(n),r=+ie(e,t)-+function(a,s){var u,f,x,b;const y=ge(),d=(s==null?void 0:s.firstWeekContainsDate)??((f=(u=s==null?void 0:s.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??y.firstWeekContainsDate??((b=(x=y.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,o=nn(a,s),c=Ot(a,0);return c.setFullYear(o,0,d),c.setHours(0,0,0,0),ie(c,s)}(e,t);return Math.round(r/tn)+1}function q(n,t){return(n<0?"-":"")+Math.abs(n).toString().padStart(t,"0")}const Tt={y(n,t){const e=n.getFullYear(),r=e>0?e:1-e;return q(t==="yy"?r%100:r,t.length)},M(n,t){const e=n.getMonth();return t==="M"?String(e+1):q(e+1,2)},d:(n,t)=>q(n.getDate(),t.length),a(n,t){const e=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.toUpperCase();case"aaa":return e;case"aaaaa":return e[0];default:return e==="am"?"a.m.":"p.m."}},h:(n,t)=>q(n.getHours()%12||12,t.length),H:(n,t)=>q(n.getHours(),t.length),m:(n,t)=>q(n.getMinutes(),t.length),s:(n,t)=>q(n.getSeconds(),t.length),S(n,t){const e=t.length,r=n.getMilliseconds();return q(Math.trunc(r*Math.pow(10,e-3)),t.length)}},Wn="midnight",zn="noon",An="morning",Rn="afternoon",En="evening",On="night",He={G:function(n,t,e){const r=n.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return e.era(r,{width:"abbreviated"});case"GGGGG":return e.era(r,{width:"narrow"});default:return e.era(r,{width:"wide"})}},y:function(n,t,e){if(t==="yo"){const r=n.getFullYear(),a=r>0?r:1-r;return e.ordinalNumber(a,{unit:"year"})}return Tt.y(n,t)},Y:function(n,t,e,r){const a=nn(n,r),s=a>0?a:1-a;return t==="YY"?q(s%100,2):t==="Yo"?e.ordinalNumber(s,{unit:"year"}):q(s,t.length)},R:function(n,t){return q(en(n),t.length)},u:function(n,t){return q(n.getFullYear(),t.length)},Q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return q(r,2);case"Qo":return e.ordinalNumber(r,{unit:"quarter"});case"QQQ":return e.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return e.quarter(r,{width:"narrow",context:"formatting"});default:return e.quarter(r,{width:"wide",context:"formatting"})}},q:function(n,t,e){const r=Math.ceil((n.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return q(r,2);case"qo":return e.ordinalNumber(r,{unit:"quarter"});case"qqq":return e.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return e.quarter(r,{width:"narrow",context:"standalone"});default:return e.quarter(r,{width:"wide",context:"standalone"})}},M:function(n,t,e){const r=n.getMonth();switch(t){case"M":case"MM":return Tt.M(n,t);case"Mo":return e.ordinalNumber(r+1,{unit:"month"});case"MMM":return e.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return e.month(r,{width:"narrow",context:"formatting"});default:return e.month(r,{width:"wide",context:"formatting"})}},L:function(n,t,e){const r=n.getMonth();switch(t){case"L":return String(r+1);case"LL":return q(r+1,2);case"Lo":return e.ordinalNumber(r+1,{unit:"month"});case"LLL":return e.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return e.month(r,{width:"narrow",context:"standalone"});default:return e.month(r,{width:"wide",context:"standalone"})}},w:function(n,t,e,r){const a=$n(n,r);return t==="wo"?e.ordinalNumber(a,{unit:"week"}):q(a,t.length)},I:function(n,t,e){const r=qn(n);return t==="Io"?e.ordinalNumber(r,{unit:"week"}):q(r,t.length)},d:function(n,t,e){return t==="do"?e.ordinalNumber(n.getDate(),{unit:"date"}):Tt.d(n,t)},D:function(n,t,e){const r=Sn(n);return t==="Do"?e.ordinalNumber(r,{unit:"dayOfYear"}):q(r,t.length)},E:function(n,t,e){const r=n.getDay();switch(t){case"E":case"EE":case"EEE":return e.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return e.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return e.day(r,{width:"short",context:"formatting"});default:return e.day(r,{width:"wide",context:"formatting"})}},e:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return q(s,2);case"eo":return e.ordinalNumber(s,{unit:"day"});case"eee":return e.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return e.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return e.day(a,{width:"short",context:"formatting"});default:return e.day(a,{width:"wide",context:"formatting"})}},c:function(n,t,e,r){const a=n.getDay(),s=(a-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return q(s,t.length);case"co":return e.ordinalNumber(s,{unit:"day"});case"ccc":return e.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return e.day(a,{width:"narrow",context:"standalone"});case"cccccc":return e.day(a,{width:"short",context:"standalone"});default:return e.day(a,{width:"wide",context:"standalone"})}},i:function(n,t,e){const r=n.getDay(),a=r===0?7:r;switch(t){case"i":return String(a);case"ii":return q(a,t.length);case"io":return e.ordinalNumber(a,{unit:"day"});case"iii":return e.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return e.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return e.day(r,{width:"short",context:"formatting"});default:return e.day(r,{width:"wide",context:"formatting"})}},a:function(n,t,e){const r=n.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return e.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return e.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return e.dayPeriod(r,{width:"narrow",context:"formatting"});default:return e.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(n,t,e){const r=n.getHours();let a;switch(a=r===12?zn:r===0?Wn:r/12>=1?"pm":"am",t){case"b":case"bb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return e.dayPeriod(a,{width:"narrow",context:"formatting"});default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(n,t,e){const r=n.getHours();let a;switch(a=r>=17?En:r>=12?Rn:r>=4?An:On,t){case"B":case"BB":case"BBB":return e.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return e.dayPeriod(a,{width:"narrow",context:"formatting"});default:return e.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(n,t,e){if(t==="ho"){let r=n.getHours()%12;return r===0&&(r=12),e.ordinalNumber(r,{unit:"hour"})}return Tt.h(n,t)},H:function(n,t,e){return t==="Ho"?e.ordinalNumber(n.getHours(),{unit:"hour"}):Tt.H(n,t)},K:function(n,t,e){const r=n.getHours()%12;return t==="Ko"?e.ordinalNumber(r,{unit:"hour"}):q(r,t.length)},k:function(n,t,e){let r=n.getHours();return r===0&&(r=24),t==="ko"?e.ordinalNumber(r,{unit:"hour"}):q(r,t.length)},m:function(n,t,e){return t==="mo"?e.ordinalNumber(n.getMinutes(),{unit:"minute"}):Tt.m(n,t)},s:function(n,t,e){return t==="so"?e.ordinalNumber(n.getSeconds(),{unit:"second"}):Tt.s(n,t)},S:function(n,t){return Tt.S(n,t)},X:function(n,t,e){const r=n.getTimezoneOffset();if(r===0)return"Z";switch(t){case"X":return Be(r);case"XXXX":case"XX":return $t(r);default:return $t(r,":")}},x:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"x":return Be(r);case"xxxx":case"xx":return $t(r);default:return $t(r,":")}},O:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Le(r,":");default:return"GMT"+$t(r,":")}},z:function(n,t,e){const r=n.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Le(r,":");default:return"GMT"+$t(r,":")}},t:function(n,t,e){return q(Math.trunc(n.getTime()/1e3),t.length)},T:function(n,t,e){return q(n.getTime(),t.length)}};function Le(n,t=""){const e=n>0?"-":"+",r=Math.abs(n),a=Math.trunc(r/60),s=r%60;return s===0?e+String(a):e+String(a)+t+q(s,2)}function Be(n,t){return n%60==0?(n>0?"-":"+")+q(Math.abs(n)/60,2):$t(n,t)}function $t(n,t=""){const e=n>0?"-":"+",r=Math.abs(n);return e+q(Math.trunc(r/60),2)+t+q(r%60,2)}const Qe=(n,t)=>{switch(n){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},Ge=(n,t)=>{switch(n){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},Fn={p:Ge,P:(n,t)=>{const e=n.match(/(P+)(p+)?/)||[],r=e[1],a=e[2];if(!a)return Qe(n,t);let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;default:s=t.dateTime({width:"full"})}return s.replace("{{date}}",Qe(r,t)).replace("{{time}}",Ge(a,t))}},jn=/^D+$/,In=/^Y+$/,Yn=["D","DD","YY","YYYY"],Hn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ln=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Bn=/^'([^]*?)'?$/,Qn=/''/g,Gn=/[a-zA-Z]/;function Xe(n,t,e){var u,f,x,b,A,N,T,l;const r=ge(),a=(e==null?void 0:e.locale)??r.locale??Cn,s=(e==null?void 0:e.firstWeekContainsDate)??((f=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:f.firstWeekContainsDate)??r.firstWeekContainsDate??((b=(x=r.locale)==null?void 0:x.options)==null?void 0:b.firstWeekContainsDate)??1,y=(e==null?void 0:e.weekStartsOn)??((N=(A=e==null?void 0:e.locale)==null?void 0:A.options)==null?void 0:N.weekStartsOn)??r.weekStartsOn??((l=(T=r.locale)==null?void 0:T.options)==null?void 0:l.weekStartsOn)??0,d=kt(n);if(!Nn(d))throw new RangeError("Invalid time value");let o=t.match(Ln).map(g=>{const k=g[0];return k==="p"||k==="P"?(0,Fn[k])(g,a.formatLong):g}).join("").match(Hn).map(g=>{if(g==="''")return{isToken:!1,value:"'"};const k=g[0];if(k==="'")return{isToken:!1,value:Xn(g)};if(He[k])return{isToken:!0,value:g};if(k.match(Gn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+k+"`");return{isToken:!1,value:g}});a.localize.preprocessor&&(o=a.localize.preprocessor(d,o));const c={firstWeekContainsDate:s,weekStartsOn:y,locale:a};return o.map(g=>{if(!g.isToken)return g.value;const k=g.value;return(!(e!=null&&e.useAdditionalWeekYearTokens)&&function(m){return In.test(m)}(k)||!(e!=null&&e.useAdditionalDayOfYearTokens)&&function(m){return jn.test(m)}(k))&&function(m,E,W){const O=function(J,tt,ot){const ft=J[0]==="Y"?"years":"days of the month";return`Use \`${J.toLowerCase()}\` instead of \`${J}\` (in \`${tt}\`) for formatting ${ft} to the input \`${ot}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(m,E,W);if(console.warn(O),Yn.includes(m))throw new RangeError(O)}(k,t,String(n)),(0,He[k[0]])(d,k,a.localize,c)}).join("")}function Xn(n){const t=n.match(Bn);return t?t[1].replace(Qn,"'"):n}function we(n,t){const e=function(d){const o={},c=d.split(ue.dateTimeDelimiter);let u;if(c.length>2)return o;if(/:/.test(c[0])?u=c[0]:(o.date=c[0],u=c[1],ue.timeZoneDelimiter.test(o.date)&&(o.date=d.split(ue.timeZoneDelimiter)[0],u=d.substr(o.date.length,d.length))),u){const f=ue.timezone.exec(u);f?(o.time=u.replace(f[1],""),o.timezone=f[1]):o.time=u}return o}(n);let r;if(e.date){const d=function(o,c){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+c)+"})|(\\d{2}|[+-]\\d{"+(2+c)+"})$)"),f=o.match(u);if(!f)return{year:NaN,restDateString:""};const x=f[1]?parseInt(f[1]):null,b=f[2]?parseInt(f[2]):null;return{year:b===null?x:100*b,restDateString:o.slice((f[1]||f[2]).length)}}(e.date,2);r=function(o,c){if(c===null)return new Date(NaN);const u=o.match(Un);if(!u)return new Date(NaN);const f=!!u[4],x=ee(u[1]),b=ee(u[2])-1,A=ee(u[3]),N=ee(u[4]),T=ee(u[5])-1;if(f)return function(l,g,k){return g>=1&&g<=53&&k>=0&&k<=6}(0,N,T)?function(l,g,k){const m=new Date(0);m.setUTCFullYear(l,0,4);const E=m.getUTCDay()||7,W=7*(g-1)+k+1-E;return m.setUTCDate(m.getUTCDate()+W),m}(c,N,T):new Date(NaN);{const l=new Date(0);return function(g,k,m){return k>=0&&k<=11&&m>=1&&m<=(Kn[k]||(Ue(g)?29:28))}(c,b,A)&&function(g,k){return k>=1&&k<=(Ue(g)?366:365)}(c,x)?(l.setUTCFullYear(c,b,Math.max(x,A)),l):new Date(NaN)}}(d.restDateString,d.year)}if(!r||isNaN(r.getTime()))return new Date(NaN);const a=r.getTime();let s,y=0;if(e.time&&(y=function(d){const o=d.match(Jn);if(!o)return NaN;const c=ye(o[1]),u=ye(o[2]),f=ye(o[3]);return function(x,b,A){return x===24?b===0&&A===0:A>=0&&A<60&&b>=0&&b<60&&x>=0&&x<25}(c,u,f)?c*Fe+u*je+1e3*f:NaN}(e.time),isNaN(y)))return new Date(NaN);if(!e.timezone){const d=new Date(a+y),o=new Date(0);return o.setFullYear(d.getUTCFullYear(),d.getUTCMonth(),d.getUTCDate()),o.setHours(d.getUTCHours(),d.getUTCMinutes(),d.getUTCSeconds(),d.getUTCMilliseconds()),o}return s=function(d){if(d==="Z")return 0;const o=d.match(Zn);if(!o)return 0;const c=o[1]==="+"?-1:1,u=parseInt(o[2]),f=o[3]&&parseInt(o[3])||0;return function(x,b){return b>=0&&b<=59}(0,f)?c*(u*Fe+f*je):NaN}(e.timezone),isNaN(s)?new Date(NaN):new Date(a+y+s)}const ue={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},Un=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,Jn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,Zn=/^([+-])(\d{2})(?::?(\d{2}))?$/;function ee(n){return n?parseInt(n):1}function ye(n){return n&&parseFloat(n.replace(",","."))||0}const Kn=[31,null,31,30,31,30,31,31,30,31,30,31];function Ue(n){return n%400==0||n%4==0&&n%100!=0}var Vn=C('<span slot="text" class="c-history-header--ellipsis svelte-8btr94"> </span>'),tr=C('<span class="c-history-header--ellipsis-left svelte-8btr94"> </span>'),er=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">File:</span> <!></div>'),nr=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Instruction:</span> <span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),rr=C('<div class="c-history-header__item svelte-8btr94"><span class="c-history-header--ellipsis svelte-8btr94"> </span></div>'),ir=C('<div class="c-history-header svelte-8btr94"><div class="c-history-header__timestamp svelte-8btr94"> </div> <div class="c-history-header__metadata svelte-8btr94"><div class="c-history-header__item svelte-8btr94"><span class="c-history-header__label svelte-8btr94">Request ID:</span> <!></div> <!> <!> <!></div></div>');function rn(n,t){Wt(t,!1);let e=H(t,"occuredAt",8),r=H(t,"requestID",8),a=H(t,"pathName",8,""),s=H(t,"repoRoot",8),y=H(t,"prompt",8,""),d=H(t,"others",24,()=>[]);function o(){Et.postMessage({type:gt.openFile,data:{repoRoot:s(),pathName:a()}})}zt();var c=ir(),u=h(c),f=h(u),x=S(u,2),b=h(x),A=S(h(b),2);fn(A,{get text(){return r()},variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:(m,E)=>{var W=Vn(),O=h(W);dt(()=>mt(O,r())),M(m,W)}}});var N=S(b,2),T=m=>{var E=er(),W=S(h(E),2);_e(W,{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$events:{click:o},children:(O,J)=>{var tt=tr(),ot=h(tt);dt(()=>mt(ot,`‎${a()??""}`)),M(O,tt)},$$slots:{default:!0}}),M(m,E)};X(N,m=>{a()&&m(T)});var l=S(N,2),g=m=>{var E=nr(),W=S(h(E),2),O=h(W);dt(()=>mt(O,y())),M(m,E)};X(l,m=>{y()&&m(g)});var k=S(l,2);Rt(k,1,d,re,(m,E)=>{var W=rr(),O=h(W),J=h(O);dt(()=>mt(J,i(E))),M(m,W)}),dt(m=>mt(f,m),[()=>(P(Xe),P(e()),w(()=>Xe(e(),"p 'on' P")))],U),M(n,c),At()}const Me={lineNumbers:"off",padding:{top:18,bottom:18}};var ar=C('<div class="c-completion-code-block svelte-krgqjl"><div class="c-completion-code-block__content svelte-krgqjl"><!></div></div>');const Je=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],ne=new class{constructor(){Ae(this,"_state");this._state=Et.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(n){return this._state.feedback[n]?this._state.feedback[n]:{selectedRating:at.unset,feedbackNote:""}}setFeedback(n,t){this._state.feedback[n]=t,Et.setState(this._state)}cleanupFeedback(n){for(const t of Object.keys(this._state.feedback))n[t]||delete this._state.feedback[t];Et.setState(this._state)}};function le(n){return typeof n=="string"?n:n==null?void 0:n.value}var or=C('<div><div class="background-slider svelte-axvozx"></div> <!></div>');function an(n,t){Wt(t,!1);let e=H(t,"options",8),r=H(t,"size",8,2),a=H(t,"disabled",8,!1),s=H(t,"onSelectOption",8),y=H(t,"activeOption",28,()=>le(e()[0])),d=D(),o=D();function c(){var k;const l=(k=i(d))==null?void 0:k.querySelectorAll(".c-toggle-button__button");if(!l)return;const g=l[e().findIndex(m=>le(m)===y())];if(i(d)&&i(o)&&g){const m=g.getBoundingClientRect(),E=i(d).getBoundingClientRect();ht(o,i(o).style.left=m.left-E.left+"px"),ht(o,i(o).style.width=`${m.width}px`),ht(o,i(o).style.height=`${m.height}px`)}}let u=D(),f=D(!1),x=D();Ze(()=>{var l;(l=i(u))==null||l.disconnect(),_(u,void 0),clearTimeout(i(x))}),B(()=>P(y()),()=>{y()&&c()}),B(()=>(i(d),i(u),i(x)),()=>{i(d)&&!i(u)&&(_(u,new ResizeObserver(()=>{_(f,!0),c(),clearTimeout(i(x)),_(x,setTimeout(()=>{_(f,!1)},100))})),i(u).observe(i(d)))}),de(),zt();var b=or();let A;var N=h(b);Re(N,l=>_(o,l),()=>i(o));var T=S(N,2);Rt(T,1,e,re,(l,g)=>{const k=U(()=>i(g)===y()?"c-toggle-button__button--active":"");gn(l,{get size(){return r()},get disabled(){return a()},variant:"ghost",color:"neutral",get class(){return`c-toggle-button__button ${i(k)??""}`},$$events:{click:()=>function(m){!a()&&s()(m)&&y(m)}(le(i(g)))},children:(m,E)=>{var W=Ke(),O=Dt(W);const J=U(()=>(i(g),w(()=>le(i(g)))));dn(O,t,"option-button-contents",{get option(){return i(J)},get size(){return r()}},tt=>{const ot=U(()=>r()===.5?1:r());mn(tt,{get size(){return i(ot)},children:(ft,wt)=>{var Ft=ke();dt(()=>mt(Ft,(i(g),w(()=>typeof i(g)=="string"?i(g):i(g).label)))),M(ft,Ft)},$$slots:{default:!0}})}),M(m,W)},$$slots:{default:!0}})}),Re(b,l=>_(d,l),()=>i(d)),dt(l=>A=Ne(b,1,"c-toggle-button svelte-axvozx",null,A,l),[()=>({"c-toggle-button--disabled":a(),"c-toggle-button--size-0_5":r()===.5,"c-toggle-button--size-1":r()===1,"c-toggle-button--size-2":r()===2,"c-toggle-button--size-3":r()===3,"c-toggle-button--size-4":r()===4,"c-toggle-button--resizing":i(f)})],U),M(n,b),At()}var sr=C('<div class="c-unified-history-item__tabs svelte-179mxe5"><!></div>'),cr=C('<div class="c-unified-history-item__code-block svelte-179mxe5"><!></div>'),ur=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext"><code><span> </span></code></pre></div> <div> </div> <section>original: <!> modified: <!></section>',1),lr=C('<div class="c-completion-code-block" role="button" tabindex="0"><pre data-language="plaintext" class="c-next-edit-addition svelte-179mxe5"><code><span> </span></code></pre></div>'),dr=C('<section><!> <div class="c-unified-history-item__no-modifications svelte-179mxe5">Unchanged locations:</div> <!></section>'),mr=C('<div class="c-unified-history-item__feedback-area svelte-179mxe5"><div class="c-unified-history-item__feedback-content svelte-179mxe5"><!></div> <div class="c-unified-history-item__feedback-actions svelte-179mxe5"><!> <!></div></div>'),hr=C('<div class="c-unified-history-item__header svelte-179mxe5"><!></div> <!> <div class="c-unified-history-item__content svelte-179mxe5"><!></div> <div class="c-unified-history-item__footer svelte-179mxe5"><div class="c-unified-history-item__ratings svelte-179mxe5"><div class="c-unified-history-item__rating-buttons svelte-179mxe5"><!> <!></div> <div class="c-unified-history-item__thankyou svelte-179mxe5"> </div></div> <!></div>',1);function xe(n,t){Wt(t,!1);const e=D(),r=D(),a=D(),s=D(),y=D(),d=D();let o=H(t,"completion",24,()=>{}),c=H(t,"nextEdit",24,()=>{}),u=H(t,"demo",8,!1),f=D(i(e)==="completion"?"Completion":"Next Edit");const x=i(e)==="completion"?["Completion"]:["Next Edit"];let b,A,N=D(ne.getFeedback(i(r))),T=D(!1),l=D(""),g=D(!1),k=null,m=D(""),E=D(),W=D([]),O=D([]);function J(){_(l,Je[Math.floor(Math.random()*Je.length)]),b&&clearTimeout(b),b=setTimeout(()=>{_(l,"")},4e3)}function tt(p){ht(N,i(N).selectedRating=p),k=p,_(m,""),_(g,!0)}function ot(){_(g,!1),k=null,_(m,""),ht(N,i(N).selectedRating=at.unset)}function ft(){k&&i(m).trim().length!==0&&(function(p,z){if(J(),A=i(N).selectedRating,p!==at.unset&&ht(N,i(N).selectedRating=p),u())return;let j=z||i(N).feedbackNote;ne.setFeedback(i(r),i(N)),_(T,!0);const Q=i(e)==="completion"?gt.completionRating:gt.nextEditRating;Et.postMessage({type:Q,data:{requestId:i(r),rating:p,note:j.trim()}})}(k,i(m).trim()),ot())}function wt(p){Et.postMessage({type:gt.openFile,data:{repoRoot:p.qualifiedPathName.rootPath,pathName:p.result.path,range:p.lineRange,differentTab:!0}}),_(E,p)}function Ft(p){return _(f,p),!0}B(()=>P(o()),()=>{_(e,o()?"completion":"nextEdit")}),B(()=>(P(o()),P(c())),()=>{var p,z;_(r,((p=o())==null?void 0:p.requestId)||((z=c())==null?void 0:z.requestId)||"")}),B(()=>(P(o()),P(c())),()=>{var p,z;_(a,((p=o())==null?void 0:p.occuredAt)||((z=c())==null?void 0:z.occurredAt)||new Date)}),B(()=>P(o()),()=>{var p;_(s,((p=o())==null?void 0:p.pathName)||"")}),B(()=>(P(o()),P(c())),()=>{var p,z,j;_(y,((p=o())==null?void 0:p.repoRoot)||((j=(z=c())==null?void 0:z.qualifiedPathName)==null?void 0:j.rootPath)||"")}),B(()=>i(e),()=>{_(d,"Leave feedback about this "+(i(e)==="completion"?"completion":"next edit"))}),B(()=>(P(c()),ve),()=>{c()&&(_(W,c().suggestions.filter(p=>p.changeType!==ve.noop)),_(O,c().suggestions.filter(p=>p.changeType===ve.noop)))}),de(),zt(),Pt("message",Ve,function(p){if(u())return;const z=p.data;switch(z.type){case gt.completionRatingDone:{const{requestId:j}=z.data;if(j!==i(r))return;_(T,!1),z.data.success||(ht(N,i(N).selectedRating=A),ne.setFeedback(i(r),i(N)));break}case gt.nextEditRatingDone:{const{requestId:j}=z.data;if(j!==i(r))return;_(T,!1),z.data.success||(ht(N,i(N).selectedRating=A),ne.setFeedback(i(r),i(N)));break}}});const fe=U(()=>"c-unified-history-item "+(i(T)?"c-unified-history-item--sending-feedback":""));xn(n,{size:2,variant:"surface",get class(){return i(fe)},children:(p,z)=>{var j=hr(),Q=Dt(j),yt=h(Q);const Ct=U(()=>(P(c()),w(()=>c()?[`Request type: ${c().mode}/${c().scope}`]:void 0)));rn(yt,{get occuredAt(){return i(a)},get requestID(){return i(r)},get pathName(){return i(s)},get repoRoot(){return i(y)},get others(){return i(Ct)}});var Nt=S(Q,2),jt=F=>{var et=sr();an(h(et),{get options(){return x},onSelectOption:Ft,get activeOption(){return i(f)},size:1}),M(F,et)};X(Nt,F=>{w(()=>x.length>1)&&F(jt)});var It=S(Nt,2),Lt=h(It),Bt=F=>{var et=Ke(),I=Dt(et);Rt(I,1,()=>(P(o()),w(()=>o().completions)),re,(Z,nt)=>{var vt=cr();(function(st,L){Wt(L,!1);let v=H(L,"prefix",8),K=H(L,"suffix",8),Y=H(L,"completion",8);const V=function(Mt){const lt=Mt.split(`
`).slice(-6);for(let xt=0;xt<lt.length;xt++)if(lt[xt].trim().length>0)return lt.slice(xt).join(`
`);return""}(v()),rt=(ct=K(),!!(bt=Y().skippedSuffix)&&ct.indexOf(bt)===0);var ct,bt;const Yt=rt?function(Mt,lt){return lt?Mt.indexOf(lt)!==0?Mt:Mt.slice(lt.length):Mt}(K(),Y().skippedSuffix):K(),St=function(Mt){const lt=Mt.split(`
`).slice(0,6);for(let xt=lt.length-1;xt>=0;xt--)if(lt[xt].trim().length>0)return lt.slice(0,xt+1).join(`
`);return""}(Yt),it=Y().text,qt=rt?Y().skippedSuffix:"",Xt=Y().suffixReplacementText,Ut=V+it+qt+Xt+St,G=wn.createModel(Ut,"plaintext"),Ht=G.getPositionAt(0),Jt=G.getPositionAt(V.length),Te=G.getPositionAt(V.length),Pe=G.getPositionAt(V.length+it.length),De=G.getPositionAt(V.length+it.length),Ce=G.getPositionAt(V.length+it.length+qt.length),Se=G.getPositionAt(V.length+it.length+qt.length),qe=G.getPositionAt(V.length+it.length+qt.length+Xt.length),$e=G.getPositionAt(V.length+it.length+qt.length+Xt.length),We=G.getPositionAt(Ut.length),on=[{range:new Zt(Ht.lineNumber,Ht.column,Jt.lineNumber,Jt.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Zt($e.lineNumber,$e.column,We.lineNumber,We.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new Zt(Te.lineNumber,Te.column,Pe.lineNumber,Pe.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Zt(Se.lineNumber,Se.column,qe.lineNumber,qe.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new Zt(De.lineNumber,De.column,Ce.lineNumber,Ce.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];Ze(()=>{G==null||G.dispose()}),zt();var ze=ar(),sn=h(ze),cn=h(sn);vn(cn,{get options(){return Me},get model(){return G},get decorations(){return on}}),M(st,ze),At()})(h(vt),{get completion(){return i(nt)},get prefix(){return P(o()),w(()=>o().prefix)},get suffix(){return P(o()),w(()=>o().suffix)}}),M(Z,vt)}),M(F,et)},Qt=(F,et)=>{var I=Z=>{var nt=dr(),vt=h(nt);Rt(vt,1,()=>i(W),re,(L,v)=>{var K=ur(),Y=Dt(K),V=Ee(()=>Ie("Enter",()=>wt(i(v)))),rt=h(Y),ct=h(rt),bt=h(ct);let Yt;var St=h(bt),it=S(Y,2),qt=h(it),Xt=S(it,2),Ut=S(h(Xt));me(Ut,{get text(){return i(v),w(()=>i(v).result.existingCode)},get pathName(){return i(v),w(()=>i(v).qualifiedPathName.relPath)},options:{lineNumbers:"off"}});var G=S(Ut,2);me(G,{get text(){return i(v),w(()=>i(v).result.suggestedCode)},get pathName(){return i(v),w(()=>i(v).qualifiedPathName.relPath)},options:{lineNumbers:"off"}}),dt(Ht=>{Yt=Ne(bt,1,"c-next-edit-addition svelte-179mxe5",null,Yt,Ht),mt(St,`${i(v),w(()=>i(v).qualifiedPathName.relPath)??""}: ${i(v),w(()=>i(v).lineRange.start+(i(v).lineRange.start<i(v).lineRange.stop?1:0))??""}-${i(v),w(()=>i(v).lineRange.stop)??""}`),mt(qt,(i(v),w(()=>i(v).result.changeDescription)))},[()=>({"c-next-edit-addition-clicked":i(E)===i(v)})],U),Pt("click",Y,()=>wt(i(v))),Pt("keydown",Y,function(...Ht){var Jt;(Jt=i(V))==null||Jt.apply(this,Ht)}),M(L,K)});var st=S(vt,4);Rt(st,1,()=>i(O),re,(L,v)=>{var K=lr(),Y=Ee(()=>Ie("Enter",()=>wt(i(v)))),V=h(K),rt=h(V),ct=h(rt);let bt;var Yt=h(ct);dt(St=>{bt=Ne(ct,1,"c-next-edit-addition svelte-179mxe5",null,bt,St),mt(Yt,`${i(v),w(()=>i(v).qualifiedPathName.relPath)??""}: ${i(v),w(()=>i(v).lineRange.start+(i(v).lineRange.start<i(v).lineRange.stop?1:0))??""}-${i(v),w(()=>i(v).lineRange.stop)??""}`)},[()=>({"c-next-edit-addition-clicked":i(E)===i(v)})],U),Pt("click",K,()=>wt(i(v))),Pt("keydown",K,function(...St){var it;(it=i(Y))==null||it.apply(this,St)}),M(L,K)}),M(Z,nt)};X(F,Z=>{i(e)==="nextEdit"&&c()&&Z(I)},et)};X(Lt,F=>{i(e)==="completion"&&o()?F(Bt):F(Qt,!1)});var R=S(It,2),ut=h(R),_t=h(ut),pt=h(_t);const $=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.positive?"success":"neutral")));Oe(pt,{variant:"ghost",get color(){return i($)},size:2,get disabled(){return i(T)},get title(){return i(d)},$$events:{click:()=>tt(at.positive)},children:(F,et)=>{const I=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.positive)));Ye(F,{iconName:"thumb_up",get fill(){return i(I)}})},$$slots:{default:!0}});var Gt=S(pt,2);const ae=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.negative?"error":"neutral")));Oe(Gt,{variant:"ghost",get color(){return i(ae)},size:2,get disabled(){return i(T)},get title(){return i(d)},$$events:{click:()=>tt(at.negative)},children:(F,et)=>{const I=U(()=>(i(N),P(at),w(()=>i(N).selectedRating===at.negative)));Ye(F,{iconName:"thumb_down",get fill(){return i(I)}})},$$slots:{default:!0}});var oe=S(_t,2),se=h(oe),ce=S(ut,2),pe=F=>{var et=mr(),I=h(et),Z=h(I);yn(Z,{rows:"4",placeholder:"Enter your feedback...",resize:"none",get value(){return i(m)},set value(v){_(m,v)},$$legacy:!0});var nt=S(I,2),vt=h(nt);_e(vt,{variant:"ghost",size:2,$$events:{click:ot},children:(v,K)=>{var Y=ke("Cancel");M(v,Y)},$$slots:{default:!0}});var st=S(vt,2);const L=U(()=>(i(m),w(()=>i(m).trim().length===0)));_e(st,{variant:"solid",size:2,get disabled(){return i(L)},$$events:{click:ft},children:(v,K)=>{var Y=ke("Share Feedback");M(v,Y)},$$slots:{default:!0}}),M(F,et)};X(ce,F=>{i(g)&&F(pe)}),dt(()=>mt(se,i(l))),M(p,j)},$$slots:{default:!0}}),At()}var gr=C(`<div class="l-no-items svelte-10bvc8"><div class="l-no-items__msg svelte-10bvc8"><h2>History.</h2> <p>As you use Augment, we'll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we'll display for each suggestion.</p></div> <div class="l-no-items__divider svelte-10bvc8"></div> <div class="l-no-items__example svelte-10bvc8"><!></div></div>`),fr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),pr=C("modified: <!>",1),vr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn">No modification to original code</div>'),br=C("<section>original: <!> <!></section>"),wr=C('<div class="c-instruction-item__no-modifications svelte-15p7ohn" role="button" tabindex="0">Click to view diff</div>'),yr=C('<div class="c-instruction-item svelte-15p7ohn"><!> <!></div>'),xr=C('<div class="l-items-list__empty svelte-5e6wj2"><!></div>'),kr=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Nr=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),_r=C('<div class="l-items-list__instructions-section svelte-5e6wj2"><h3 class="l-items-list__section-title svelte-5e6wj2">Instructions</h3> <div class="l-items-list__content svelte-5e6wj2"></div></div>'),Mr=C('<div class="l-items-list__empty-panel svelte-5e6wj2"><p> </p></div>'),Tr=C('<div class="l-items-list__divider svelte-5e6wj2"></div>'),Pr=C('<div class="l-items-list__item svelte-5e6wj2"><!></div> <!>',1),Dr=C('<div class="l-items-list__content svelte-5e6wj2"></div>'),Cr=C('<!> <!> <div class="l-items-list__panel-content"><!></div>',1),Sr=C('<main class="l-items-list svelte-5e6wj2"><!></main>');hn(function(n,t){Wt(t,!1);const e=D(),r=D(),a=D(),s=D(),y=D();let d=D({}),o=D({}),c=D({});function u(T){for(const l of T)i(d)[l.requestId]||ht(d,i(d)[l.requestId]={...l,occuredAt:we(l.occuredAt)});ne.cleanupFeedback(i(d))}function f(T){for(const l of T)if(!i(o)[l.requestId]){if(typeof l.occuredAt=="string"){const g=l.occuredAt;l.occuredAt=we(g)}ht(o,i(o)[l.requestId]={...l})}}function x(T){for(const l of T)l.suggestions.length!==0&&ht(c,i(c)[l.requestId]={requestId:l.requestId,occuredAt:we(l.occurredAt),result:l})}Et.postMessage({type:gt.historyLoaded});let b=D([]),A=D("Completions");function N(T){return _(A,T),!0}B(()=>(i(o),i(d),i(c)),()=>{_(b,[...Object.values(i(o)),...Object.values(i(d)),...Object.values(i(c))].sort((T,l)=>l.occuredAt.getTime()-T.occuredAt.getTime()))}),B(()=>i(b),()=>{_(e,i(b).filter(T=>"completions"in T))}),B(()=>i(b),()=>{_(r,i(b).filter(T=>"result"in T))}),B(()=>i(b),()=>{_(a,i(b).filter(T=>"prompt"in T))}),B(()=>(i(e),i(r)),()=>{_(s,[{value:"Completions",label:`Completions ${i(e).length}`},{value:"Next Edits",label:`Next Edits ${i(r).length}`}])}),B(()=>(i(A),i(e),i(r)),()=>{_(y,i(A)==="Completions"?i(e):i(r))}),de(),zt(),Pt("message",Ve,function(T){const l=T.data;switch(l.type){case gt.historyInitialize:f(l.data.instructions),u(l.data.completionRequests),x(l.data.nextEdits);break;case gt.completions:u(l.data);break;case gt.instructions:f(l.data);break;case gt.nextEditSuggestions:x([l.data])}}),bn.Root(n,{children:(T,l)=>{var g=Sr(),k=h(g),m=W=>{var O=xr();(function(J,tt){Wt(tt,!1);const ot={occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`};zt();var ft=gr(),wt=S(h(ft),4);xe(h(wt),{get completion(){return ot},demo:!0}),M(J,ft),At()})(h(O),{}),M(W,O)},E=W=>{var O=Cr(),J=Dt(O);an(J,{get options(){return i(s)},onSelectOption:N,get activeOption(){return i(A)},size:2});var tt=S(J,2),ot=p=>{var z=_r(),j=S(h(z),2);Rt(j,7,()=>i(a),Q=>Q.requestId,(Q,yt,Ct)=>{var Nt=Nr(),jt=Dt(Nt),It=h(jt);const Lt=U(()=>(i(yt),w(()=>function(R){if(!("prompt"in R))throw new Error("wrong type");if("completions"in R)throw new Error("wrong type");return R}(i(yt)))));(function(R,ut){Wt(ut,!1);const _t=D(),pt=D();let $=H(ut,"instruction",8),Gt=D(void 0);function ae(){_(Gt,$().requestId)}function oe(I){const Z=I.split(`
`);for(let nt=Z.length-1;nt>=0;nt--)if(Z[nt].trim().length>0)return Z.slice(0,nt+1).join(`
`);return""}B(()=>P($()),()=>{_(_t,oe($().selectedText))}),B(()=>P($()),()=>{_(pt,oe($().modifiedText))}),de(),zt();var se=yr(),ce=h(se);rn(ce,{get occuredAt(){return P($()),w(()=>$().occuredAt)},get requestID(){return P($()),w(()=>$().requestId)},get pathName(){return P($()),w(()=>$().pathName)},get repoRoot(){return P($()),w(()=>$().repoRoot)},get prompt(){return P($()),w(()=>$().prompt)}});var pe=S(ce,2),F=I=>{var Z=fr();M(I,Z)},et=(I,Z)=>{var nt=st=>{var L=br(),v=S(h(L));me(v,{get options(){return Me},get text(){return i(_t)},get pathName(){return P($()),w(()=>$().pathName)}});var K=S(v,2),Y=rt=>{var ct=pr(),bt=S(Dt(ct));me(bt,{get options(){return Me},get text(){return i(pt)},get pathName(){return P($()),w(()=>$().pathName)}}),M(rt,ct)},V=rt=>{var ct=vr();M(rt,ct)};X(K,rt=>{i(_t)!==i(pt)?rt(Y):rt(V,!1)}),M(st,L)},vt=st=>{var L=wr();Pt("keyup",L,ae),Pt("click",L,ae),M(st,L)};X(I,st=>{P($()),i(Gt),w(()=>$().userRequested||i(Gt)===$().requestId)?st(nt):st(vt,!1)},Z)};X(pe,I=>{P($()),w(()=>$().selectedText===$().modifiedText)?I(F):I(et,!1)}),M(R,se),At()})(It,{get instruction(){return i(Lt)}});var Bt=S(jt,2),Qt=R=>{var ut=kr();M(R,ut)};X(Bt,R=>{P(i(Ct)),i(a),w(()=>i(Ct)<i(a).length-1)&&R(Qt)}),M(Q,Nt)}),M(p,z)};X(tt,p=>{i(a),w(()=>i(a).length>0)&&p(ot)});var ft=S(tt,2),wt=h(ft),Ft=p=>{var z=Mr(),j=h(z),Q=h(j);dt(yt=>mt(Q,`No ${yt??""} found.`),[()=>(i(A),w(()=>i(A).toLowerCase()))],U),M(p,z)},fe=p=>{var z=Dr();Rt(z,7,()=>i(y),j=>j.requestId,(j,Q,yt)=>{var Ct=Pr(),Nt=Dt(Ct),jt=h(Nt),It=R=>{xe(R,{get completion(){return i(Q)}})},Lt=(R,ut)=>{var _t=pt=>{xe(pt,{get nextEdit(){return i(Q),w(()=>i(Q).result)}})};X(R,pt=>{"result"in i(Q)&&pt(_t)},ut)};X(jt,R=>{"completions"in i(Q)?R(It):R(Lt,!1)});var Bt=S(Nt,2),Qt=R=>{var ut=Tr();M(R,ut)};X(Bt,R=>{P(i(yt)),i(y),w(()=>i(yt)<i(y).length-1)&&R(Qt)}),M(j,Ct)}),M(p,z)};X(wt,p=>{i(y),w(()=>i(y).length===0)?p(Ft):p(fe,!1)}),M(W,O)};X(k,W=>{i(b),w(()=>!i(b).length)?W(m):W(E,!1)}),M(T,g)},$$slots:{default:!0}}),At()},{target:document.getElementById("app")});
