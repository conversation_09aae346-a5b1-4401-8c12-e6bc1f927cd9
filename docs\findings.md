# Augment VSCode 插件逆向分析报告

## 项目概述

- **插件名称**: Augment
- **版本**: 0.524.1
- **类型**: AI 编程助手 VSCode 插件
- **主要功能**: 代码补全、聊天、Agent 模式、Remote Agent 等

## "Send to Agent" 功能分析

### 🎯 核心发现

#### 1. 按钮文本逻辑

位置: `common-webviews/assets/main-panel-CqjyrC3k.js`

```javascript
// 按钮文本动态显示逻辑
sendModeModel.isAgent && flags.enableTaskList
  ? taskModel?.label || "Send to Agent"
  : "Send message";
```

#### 2. 发送流程

```javascript
// 核心发送函数 (Oo函数)
async function Oo(agentModel, conversationModel) {
  await agentModel.interruptAgent(); // 中断当前Agent
  await conversationModel.sendDraftExchange(); // 发送草稿消息
}

// 模式处理函数 (Yh函数)
async function Yh(mode, conversation, agent, flags, taskModel) {
  switch (mode) {
    case SendMode.send:
      return await Oo(agent, conversation);
    case SendMode.addTask:
      // 任务创建逻辑
      break;
  }
}
```

### 🔗 消息通信架构

#### 1. 消息类型定义

位置: `common-webviews/assets/IconButtonAugment-B8y0FMb_.js`

关键消息类型包括:

- `chatUserMessage`: 用户聊天消息
- `chatModelReply`: 模型回复
- `remoteAgentChatRequest`: Remote Agent 聊天请求
- `remoteAgentChatResponse`: Remote Agent 聊天响应
- `callTool`: 工具调用
- `asyncWrapper`: 异步消息包装

#### 2. 消息发送机制

位置: `common-webviews/assets/message-broker-BauNv3yh.js`

```javascript
class MessageBroker extends AsyncMsgSender {
  constructor(host) {
    super((_) => {
      this._host.postMessage(_); // 发送到VSCode扩展
    });
  }

  postMessage(message) {
    this._host.postMessage(message);
  }
}
```

#### 3. 异步消息处理

位置: `common-webviews/assets/async-messaging-BnOo7nYC.js`

```javascript
// 发送消息并等待响应
async send(message, timeoutMs = this._timeoutMs) {
    const wrappedMsg = wrapMessage(message);
    const promise = this.registerPromiseContext(wrappedMsg);
    this.sendOrTimeout(wrappedMsg, timeoutMs);
    const response = await promise;

    if (response.error) throw new Error(response.error);
    return response.baseMsg;
}
```

### 🔐 Token 和认证机制

#### 1. API Token 配置

位置: `package.json`

```json
{
  "augment.advanced.apiToken": {
    "type": "string",
    "default": "",
    "description": "API token for Augment access."
  },
  "augment.advanced.completionURL": {
    "type": "string",
    "default": "",
    "description": "URL of completion server."
  }
}
```

#### 2. 第三方集成 Token

```json
{
  "augment.advanced.integrations": {
    "atlassian": {
      "personalApiToken": "string"
    },
    "notion": {
      "apiToken": "string"
    },
    "linear": {
      "apiToken": "string"
    }
  }
}
```

### 📡 Remote Agent 通信

#### 1. Remote Agent 客户端

位置: `common-webviews/assets/remote-agents-client-XI3B217g.js`

```javascript
class RemoteAgentsClient {
  async sendRemoteAgentChatRequest(agentId, requestDetails, timeoutMs = 90000) {
    return this._msgBroker.send(
      {
        type: MessageType.remoteAgentChatRequest,
        data: {
          agentId: agentId,
          requestDetails: requestDetails,
          timeoutMs: timeoutMs,
        },
      },
      timeoutMs
    );
  }
}
```

#### 2. 关键 API 调用

- `createRemoteAgent`: 创建远程 Agent
- `sendRemoteAgentChatRequest`: 发送聊天请求到远程 Agent
- `interruptRemoteAgent`: 中断远程 Agent
- `getRemoteAgentChatHistory`: 获取聊天历史

### 🔄 数据流程总结

1. **用户点击 "Send to Agent"**
2. **检查模式**: 判断是发送消息还是添加任务
3. **中断 Agent**: 如果有正在运行的 Agent，先中断
4. **发送消息**: 调用 `sendDraftExchange()`
5. **消息包装**: 使用 `asyncWrapper` 包装消息
6. **发送到扩展**: 通过 `postMessage` 发送到 VSCode 扩展后端
7. **后端处理**: 扩展后端使用 API Token 与服务器通信
8. **响应处理**: 接收响应并更新 UI

### 🔐 Token 使用和 HTTP 请求机制 ✅

#### 1. API Token 获取机制

位置: `out/extension.js` 行 206062-206068

```javascript
class ClientAuth {
  async getAPIToken() {
    if (this.auth.useOAuth) {
      let session = await this.auth.getSession();
      if (session) return session.accessToken;
    }
    return this.configListener.config.apiToken; // 从配置获取
  }
}
```

#### 2. HTTP 请求发送机制

位置: `out/extension.js` 行 176158-176189

```javascript
async callApiStream(requestId, config, endpoint, body, ...) {
    // 1. 获取API Token
    let apiToken = await this.clientAuth.getAPIToken();

    // 2. 获取服务器URL
    let serverUrl = await this.clientAuth.getCompletionURL();
    if (!serverUrl) throw new Error("Please configure Augment API URL");

    // 3. 构建完整URL
    let fullUrl = new URL(endpoint, serverUrl);

    // 4. 构建请求头
    let headers = {
        "Content-Type": "application/json",
        "User-Agent": this.userAgent,
        "x-request-id": `${requestId}`,
        "x-request-session-id": `${sessionId}`
    };

    // 5. 添加Bearer Token认证
    if (apiToken) {
        headers.Authorization = `Bearer ${apiToken}`;
    }

    // 6. 发送HTTP请求
    let response = await fetch(fullUrl.toString(), {
        method: "POST",
        headers: headers,
        body: JSON.stringify(body),
        signal: abortSignal
    });
}
```

#### 3. 认证方式确认

- **认证类型**: Bearer Token
- **Token 来源**:
  - OAuth 模式: `session.accessToken`
  - 配置模式: `config.apiToken`
- **传输方式**: HTTP Authorization 头
- **格式**: `Authorization: Bearer ${token}`

#### 4. 请求格式

- **方法**: POST
- **Content-Type**: application/json
- **自定义头**:
  - `x-request-id`: 请求唯一标识
  - `x-request-session-id`: 会话标识
  - `User-Agent`: 客户端标识

### ✅ 已解决的问题

1. ✅ **Token 传输**: 通过 `Authorization: Bearer ${token}` 头传输
2. ✅ **服务器端点**: 从 `augment.advanced.completionURL` 配置获取
3. ✅ **请求格式**: POST 请求，JSON 格式，包含自定义请求头
4. ✅ **认证方式**: Bearer Token 认证，支持 OAuth 和配置文件两种方式

### 🌐 API 端点分析 ✅

#### 1. 核心聊天相关端点

位置: `out/extension.js`

**主要聊天端点:**

- `chat-stream`: 普通聊天流式响应
- `chat`: 普通聊天请求
- `instruction-stream`: 指令流式处理
- `smart-paste-stream`: 智能粘贴流式处理

**Remote Agent 端点:**

- `remote-agents/chat`: **发送消息到 Remote Agent** ⭐
- `remote-agents/create`: 创建远程 Agent
- `remote-agents/delete`: 删除远程 Agent
- `remote-agents/interrupt`: 中断远程 Agent
- `remote-agents/list`: 列出远程 Agent
- `remote-agents/list-stream`: 流式列出远程 Agent
- `remote-agents/get-chat-history`: 获取聊天历史
- `remote-agents/agent-history-stream`: 流式获取 Agent 历史
- `remote-agents/pause`: 暂停远程 Agent 工作区
- `remote-agents/resume`: 恢复远程 Agent 工作区
- `remote-agents/update`: 更新远程 Agent 标题
- `remote-agents/resume-hint`: 恢复提示远程 Agent
- `remote-agents/add-ssh-key`: 添加 SSH 密钥
- `remote-agents/logs`: 获取工作区日志

#### 2. "Send to Agent" 的具体请求体

位置: `out/extension.js` 行 177270-177283

```javascript
async remoteAgentChat(agentId, requestDetails, timeoutMs) {
    let requestId = this.createRequestId();
    let config = this._configListener.config;
    let payload = {
        remote_agent_id: agentId,
        request_details: {
            request_nodes: requestDetails.request_nodes,
            user_guidelines: requestDetails.user_guidelines ?? config.chat.userGuidelines,
            workspace_guidelines: requestDetails.workspace_guidelines ?? "",
            agent_memories: requestDetails.agent_memories ?? "",
            model_id: requestDetails.model_id,
            mcp_servers: requestDetails.mcp_servers
        }
    };

    let response = await this.callApi(requestId, config, "remote-agents/chat", payload, undefined, undefined, timeoutMs);
    return {
        remoteAgentId: response.remote_agent_id,
        nodes: response.nodes
    };
}
```

#### 3. 其他重要端点

- `completion`: 代码补全
- `edit`: 代码编辑
- `next-edit-stream`: Next Edit 功能
- `memorize`: 记忆化
- `batch-upload`: 批量上传
- `find-missing`: 查找缺失项
- `get-models`: 获取模型列表
- `subscription-info`: 订阅信息
- `github/*`: GitHub 集成相关
- `agents/*`: Agent 工具相关
- `/save-chat`: 保存聊天记录
- `/user-secrets/*`: 用户密钥管理

## 🎯 "Send to Agent" 完整流程总结

### 📊 数据流程图

```
用户点击"Send to Agent"
    ↓
检查发送模式 (SendMode.send vs SendMode.addTask)
    ↓
调用 sendDraftExchange()
    ↓
包装为 asyncWrapper 消息
    ↓
通过 postMessage 发送到 VSCode 扩展后端
    ↓
扩展后端获取 API Token 和服务器 URL
    ↓
构建 HTTP 请求:
  - URL: ${completionURL}/remote-agents/chat
  - Method: POST
  - Headers: Authorization: Bearer ${apiToken}
  - Body: { remote_agent_id, request_details }
    ↓
发送到 Augment 服务器
    ↓
接收响应并更新 UI
```

### 🔑 关键技术细节

1. **认证方式**: Bearer Token (JWT 或 API Token)
2. **主要端点**: `remote-agents/chat`
3. **请求方法**: POST
4. **数据格式**: JSON
5. **超时设置**: 90 秒 (90000ms)
6. **重试机制**: 支持指数退避重试

### 🛡️ 安全机制

1. **Token 存储**:
   - OAuth: 存储在 VSCode 会话中
   - 配置: 存储在 `augment.advanced.apiToken`
2. **传输安全**: HTTPS + Bearer Token
3. **请求标识**: 每个请求都有唯一的 request-id
4. **会话管理**: 支持会话级别的标识

### 📋 下一步分析计划

1. ✅ 分析扩展后端代码 (`out/extension.js`)
2. ✅ 查找 HTTP 请求的具体实现
3. ✅ 分析 Token 的使用和传输方式
4. ✅ 分析具体的 API 端点和请求体格式
5. 🔄 监控实际的网络请求（动态分析）
6. 🔄 分析请求体中的具体字段含义
7. 🔄 研究响应格式和错误处理机制

---

## 📝 研究结论

通过对 Augment VSCode 插件的逆向分析，我们成功解析了 "Send to Agent" 功能的完整工作流程：

1. **前端交互**: 用户在 webview 中点击按钮触发消息发送
2. **消息路由**: 通过 VSCode 的 postMessage API 将消息路由到扩展后端
3. **认证处理**: 扩展后端获取配置的 API Token 或 OAuth 会话
4. **HTTP 请求**: 构建标准的 HTTPS POST 请求发送到 `remote-agents/chat` 端点
5. **数据传输**: 使用 Bearer Token 认证，JSON 格式传输用户消息和上下文信息

这个分析为理解现代 AI 编程助手的架构提供了宝贵的洞察，展示了如何在 VSCode 扩展中实现安全的 AI 服务集成。
