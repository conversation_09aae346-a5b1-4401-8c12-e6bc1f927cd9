function u(c,f){let n,o,t=f;const d=()=>t.editor.getModifiedEditor(),s=()=>{const{afterLineNumber:e}=t,r=d();if(e===void 0)return void r.changeViewZones(a=>{n&&r&&o&&a.removeZone(o)});const i={...t,afterLineNumber:e,domNode:c,suppressMouseDown:!0};r==null||r.changeViewZones(a=>{n&&o&&a.removeZone(o),o=a.addZone(i),n=i})};return s(),{update:e=>{t=e,s()},destroy:()=>{const e=d();e.changeViewZones(r=>{if(n&&e&&o)try{r.removeZone(o)}catch(i){if(i instanceof Error){if(i.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${i}`)}})}}}function g(c,f,n){let o,t=f;const d=()=>t.editor.getModifiedEditor(),s=()=>{const e=d();if(!e)return;const r={getDomNode:()=>c,getId:()=>t.id,getPosition:()=>({preference:n.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};o&&e.removeOverlayWidget(o),e.addOverlayWidget(r),o=r};return s(),{update:e=>{t=e,s()},destroy:()=>{const e=d();e&&o&&e.removeOverlayWidget(o)}}}export{u as a,g as r};
