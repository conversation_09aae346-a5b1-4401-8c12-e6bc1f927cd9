@echo off
REM Augment VSCode Plugin Token Injection Script for Windows
REM 使用方法: inject-token.bat

echo ========================================
echo Augment VSCode Token 注入工具
echo ========================================
echo.

REM 检查是否提供了参数
if "%1"=="clean" goto clean
if "%1"=="show" goto show
if "%1"=="help" goto help

:inject
echo 🚀 开始注入token...
echo.

REM 设置你的token和URL (在这里修改)
set "TOKEN=YOUR_JWT_TOKEN_HERE"
set "TENANT_URL=YOUR_TENANT_URL_HERE"

REM 检查是否设置了token
if "%TOKEN%"=="YOUR_JWT_TOKEN_HERE" (
    echo ❌ 错误: 请先在脚本中设置你的JWT token
    echo 编辑 inject-token.bat 文件，修改 TOKEN 变量
    pause
    exit /b 1
)

if "%TENANT_URL%"=="YOUR_TENANT_URL_HERE" (
    echo ❌ 错误: 请先在脚本中设置你的tenant URL
    echo 编辑 inject-token.bat 文件，修改 TENANT_URL 变量
    pause
    exit /b 1
)

REM VSCode设置文件路径
set "SETTINGS_PATH=%APPDATA%\Code\User\settings.json"
set "BACKUP_PATH=%SETTINGS_PATH%.backup.%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"

echo 📁 VSCode设置文件: %SETTINGS_PATH%
echo.

REM 创建目录如果不存在
if not exist "%APPDATA%\Code\User" (
    mkdir "%APPDATA%\Code\User"
    echo 📁 创建VSCode用户目录
)

REM 备份现有设置
if exist "%SETTINGS_PATH%" (
    copy "%SETTINGS_PATH%" "%BACKUP_PATH%" >nul
    echo 💾 已备份现有设置
)

REM 创建临时PowerShell脚本来处理JSON
set "PS_SCRIPT=%TEMP%\augment_inject.ps1"

echo # PowerShell script to inject Augment token > "%PS_SCRIPT%"
echo $settingsPath = '%SETTINGS_PATH%' >> "%PS_SCRIPT%"
echo $token = '%TOKEN%' >> "%PS_SCRIPT%"
echo $tenantUrl = '%TENANT_URL%' >> "%PS_SCRIPT%"
echo. >> "%PS_SCRIPT%"
echo # Read existing settings >> "%PS_SCRIPT%"
echo if (Test-Path $settingsPath) { >> "%PS_SCRIPT%"
echo     try { >> "%PS_SCRIPT%"
echo         $settings = Get-Content $settingsPath -Raw ^| ConvertFrom-Json >> "%PS_SCRIPT%"
echo     } catch { >> "%PS_SCRIPT%"
echo         $settings = @{} >> "%PS_SCRIPT%"
echo     } >> "%PS_SCRIPT%"
echo } else { >> "%PS_SCRIPT%"
echo     $settings = @{} >> "%PS_SCRIPT%"
echo } >> "%PS_SCRIPT%"
echo. >> "%PS_SCRIPT%"
echo # Add Augment configuration >> "%PS_SCRIPT%"
echo $settings ^| Add-Member -Type NoteProperty -Name 'augment.advanced.apiToken' -Value $token -Force >> "%PS_SCRIPT%"
echo $settings ^| Add-Member -Type NoteProperty -Name 'augment.advanced.completionURL' -Value $tenantUrl -Force >> "%PS_SCRIPT%"
echo. >> "%PS_SCRIPT%"
echo # Remove OAuth config if exists >> "%PS_SCRIPT%"
echo if ($settings.PSObject.Properties.Name -contains 'augment.advanced.oauth') { >> "%PS_SCRIPT%"
echo     $settings.PSObject.Properties.Remove('augment.advanced.oauth') >> "%PS_SCRIPT%"
echo } >> "%PS_SCRIPT%"
echo. >> "%PS_SCRIPT%"
echo # Write settings >> "%PS_SCRIPT%"
echo $settings ^| ConvertTo-Json -Depth 10 ^| Set-Content $settingsPath -Encoding UTF8 >> "%PS_SCRIPT%"
echo Write-Host "✅ Token注入成功!" >> "%PS_SCRIPT%"

REM 执行PowerShell脚本
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"

REM 清理临时文件
del "%PS_SCRIPT%"

echo.
echo 🔑 API Token: %TOKEN:~0,20%...
echo 🌐 Tenant URL: %TENANT_URL%
echo.
echo 📋 下一步:
echo 1. 重启VSCode
echo 2. 打开Augment聊天界面 (Ctrl+Shift+P -^> "Augment: Open Chat")
echo 3. 发送测试消息
echo 4. 检查是否收到响应
echo.
pause
exit /b 0

:clean
echo 🧹 清理Augment token...
set "SETTINGS_PATH=%APPDATA%\Code\User\settings.json"

if not exist "%SETTINGS_PATH%" (
    echo ❌ 未找到VSCode设置文件
    pause
    exit /b 1
)

REM 创建临时PowerShell脚本来清理JSON
set "PS_SCRIPT=%TEMP%\augment_clean.ps1"

echo # PowerShell script to clean Augment token > "%PS_SCRIPT%"
echo $settingsPath = '%SETTINGS_PATH%' >> "%PS_SCRIPT%"
echo. >> "%PS_SCRIPT%"
echo try { >> "%PS_SCRIPT%"
echo     $settings = Get-Content $settingsPath -Raw ^| ConvertFrom-Json >> "%PS_SCRIPT%"
echo     $settings.PSObject.Properties.Remove('augment.advanced.apiToken') >> "%PS_SCRIPT%"
echo     $settings.PSObject.Properties.Remove('augment.advanced.completionURL') >> "%PS_SCRIPT%"
echo     $settings ^| ConvertTo-Json -Depth 10 ^| Set-Content $settingsPath -Encoding UTF8 >> "%PS_SCRIPT%"
echo     Write-Host "✅ Token清理完成!" >> "%PS_SCRIPT%"
echo } catch { >> "%PS_SCRIPT%"
echo     Write-Host "❌ 清理失败: $_" >> "%PS_SCRIPT%"
echo } >> "%PS_SCRIPT%"

powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
del "%PS_SCRIPT%"

echo 请重启VSCode以使更改生效
pause
exit /b 0

:show
echo 📋 当前Augment配置:
set "SETTINGS_PATH=%APPDATA%\Code\User\settings.json"

if not exist "%SETTINGS_PATH%" (
    echo 未找到VSCode设置文件
    pause
    exit /b 1
)

REM 使用PowerShell显示配置
powershell -Command "try { $s = Get-Content '%SETTINGS_PATH%' -Raw | ConvertFrom-Json; Write-Host 'API Token:' $s.'augment.advanced.apiToken'; Write-Host 'Completion URL:' $s.'augment.advanced.completionURL' } catch { Write-Host '读取配置失败' }"
pause
exit /b 0

:help
echo 使用方法:
echo   inject-token.bat        - 注入token (默认)
echo   inject-token.bat clean  - 清理token
echo   inject-token.bat show   - 显示当前配置
echo   inject-token.bat help   - 显示帮助
echo.
echo 使用前请编辑脚本，设置你的TOKEN和TENANT_URL
pause
exit /b 0
