import{l as I,A as U,C as s,a6 as X,D as Z,J as T,G as ee,N as g,t as o,M as r,a7 as te,H as S,_ as $,m as V,b as f,I as se,Y as h,T as D,V as F,Q as ae,W as re}from"./SpinnerAugment-BY2Lraps.js";import"./IconButtonAugment-B8y0FMb_.js";import"./BaseTextInput-C64uUToe.js";import{T as ne}from"./TextAreaAugment-BkN7aH6v.js";import{l as oe}from"./chevron-down-CVLGkBkY.js";var ce=T('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1);function me(K,t){const M=I(t,["children","$$slots","$$events","$$legacy"]),A=I(M,["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"]);U(t,!1);let y,C=s(t,"variant",8,"surface"),G=s(t,"size",8,2),H=s(t,"color",24,()=>{}),J=s(t,"resize",8,"none"),a=s(t,"textInput",28,()=>{}),c=s(t,"value",12,""),k=s(t,"selectedText",12,""),l=s(t,"selectionStart",12,0),i=s(t,"selectionEnd",12,0),N=s(t,"saveFunction",8),Q=s(t,"debounceValue",8,2500),d=V(!1),u=V();const v=async()=>{try{N()(),h(d,!0),clearTimeout(y),y=setTimeout(()=>{h(d,!1)},1500)}catch(e){h(u,e instanceof Error?e.message:String(e))}};function m(){a()&&(l(a().selectionStart),i(a().selectionEnd),l()!==i()?k(c().substring(l(),i())):k(""))}const W=oe.debounce(v,Q());X(()=>{v()}),Z();var w=ce(),z=ee(w),x=o(z),Y=o(x);g(Y,t,"header",{},null);var _=r(x,2);g(_,t,"default",{},null);var j=r(_,2),E=o(j);g(E,t,"title",{},null);var q=r(E,2);ne(q,te({get variant(){return C()},get size(){return G()},get color(){return H()},get resize(){return J()},placeholder:"Enter markdown content..."},()=>A,{get textInput(){return a()},set textInput(e){a(e)},get value(){return c()},set value(e){c(e)},$$events:{select:m,mouseup:m,keyup:()=>{m()},input:W,keydown:e=>{(e.key==="Escape"||(e.metaKey||e.ctrlKey)&&e.key==="s")&&(e.preventDefault(),v())}},$$legacy:!0}));var B=r(z,2),b=o(B),L=e=>{D(e,{size:1,weight:"light",color:"error",children:(p,R)=>{var n=F();ae(()=>re(n,$(u))),f(p,n)},$$slots:{default:!0}})};S(b,e=>{$(u)&&e(L)});var O=r(b,2),P=e=>{D(e,{size:1,weight:"light",color:"success",children:(p,R)=>{var n=F("Saved");f(p,n)},$$slots:{default:!0}})};S(O,e=>{$(d)&&e(P)}),f(K,w),se()}export{me as M};
