const _0x592fd3 = _0x2a3b;

function _0x1374() {
  const _0xa1f324 = ['PUT', '一键更新失败:\x20', 'verified_at', '4hLBqav', 'VSCode-AugmentTokenManager/1.0.0', 'getAvailableTokens', 'apiService', 'tokenManager', '获取失败', 'isArray', 'currentUserData', '没有找到可用的token', 'get', '用户未验证或缺少用户Token', 'machineCode', 'callExpiryTimeApi', 'checkSavedUserToken', 'getCardMachineCode', 'globalState', 'workspace', 'checkHeartbeat', 'success', 'updateTokenUserCk', 'getWebviewContent', 'Bearer\x20', 'quickUpdate', 'error', 'isInitialized', '正在一键更新Token...', 'hostname', 'data', 'request', 'sidebarProvider', 'statusText', 'Unknown\x20error', 'HTTP\x20', 'command', 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', '缺少用户Token', 'onDidReceiveMessage', 'tokens', 'https://augmenttoken.159email.shop', '一键更新成功！随机选择了第', 'startHeartbeat', '_view', 'crypto', 'url', '805251WJuPkB', 'sendMessage', 'secrets', 'resolveWebviewView', 'user_active', 'No\x20session\x20data\x20found', 'random', 'verifySuccess', 'floor', 'registerWebviewViewProvider', 'augment.sessions', 'token', 'statusCode', 'settingsLoaded', 'testConnection', 'createOrShow', 'map', 'Failed\x20to\x20parse\x20JSON\x20response', 'savedUserTokenFound', 'searchParams', '用户验证失败:\x20', 'update', 'validateSettings', '9LheMAl', 'parse', 'toString', 'length', 'connectionTestResult', 'tenantURL', 'ViewColumn', 'createWebviewPanel', '107365bURptM', 'push', '5530540pEZqDP', 'statusMessage', 'Settings\x20validation\x20failed:\x20', 'updated_at', 'apiBaseUrl', 'API\x20Base\x20URL\x20is\x20required', 'limit', 'store', 'Settings\x20reset\x20to\x20defaults!', 'postMessage', 'dispose', 'Default\x20Tenant\x20URL\x20is\x20required', '12VUdRWe', 'noSavedUserToken', 'settingsManager', 'logoutSuccess', 'use_time', 'end', 'https', 'Machine\x20code\x20updated:\x20', 'cardMachineCode', 'handleMessage', 'Failed\x20to\x20get\x20token\x20list:\x20', 'Invalid\x20API\x20response\x20format\x20or\x20API\x20returned\x20error', 'resetSettings', 'Failed\x20to\x20update\x20machine\x20code:\x20', 'saveSettings', 'GET', 'user_ck', 'context', 'exports', 'includes', 'augmentTokenManager', 'activeTextEditor', 'Failed\x20to\x20save\x20settings', 'getConfiguration', 'userToken', 'subscriptions', 'webview', 'search', 'callUserVerifyApi', 'augment_external_v1_2024', 'savedUserToken', '已退出登录并清除保存的卡密', '/api/external/v1/tokens', 'panel', 'user', 'destroy', 'json', 'verifyFailed', 'timeout', 'viewColumn', 'stringify', 'Failed\x20to\x20load\x20settings:\x20', 'created_at', 'loading', 'getMachineCode', '1616082edqDrq', 'initialize', 'Request\x20timeout', 'augmentTokenManagerSettings', 'getApiBaseUrl', 'randomUUID', 'protocol', '2407032LMJpcC', '220750JXNoOs', 'https://d5.api.augmentcode.com/', 'status', 'errors', 'fetch\x20is\x20not\x20defined', 'updateMachineCode', 'logout', '用户未激活或不存在', 'resetToDefaults', 'https:', '<!DOCTYPE\x20html>\x0a<html\x20lang=\x22zh-CN\x22>\x0a<head>\x0a\x20\x20\x20\x20<meta\x20charset=\x22UTF-8\x22>\x0a\x20\x20\x20\x20<meta\x20name=\x22viewport\x22\x20content=\x22width=device-width,\x20initial-scale=1.0\x22>\x0a\x20\x20\x20\x20<meta\x20http-equiv=\x22Content-Security-Policy\x22\x20content=\x22default-src\x20\x27none\x27;\x20style-src\x20\x27unsafe-inline\x27;\x20script-src\x20\x27unsafe-inline\x27;\x22>\x0a\x20\x20\x20\x20<title>Token\x20Manager</title>\x0a\x20\x20\x20\x20<style>\x0a\x20\x20\x20\x20\x20\x20\x20\x20body\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20-apple-system,\x20BlinkMacSystemFont,\x20\x27Segoe\x20UI\x27,\x20Roboto,\x20sans-serif;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin:\x200;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2016px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-sideBar-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-sideBar-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2013px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.section\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2020px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-editor-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20var(--vscode-panel-border);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.section-title\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-weight:\x20600;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-textLink-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2014px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-button-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x20none;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20cursor:\x20pointer;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-right:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20width:\x20100%;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button:hover\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-hoverBackground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.token-info\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2011px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-descriptionForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-textCodeBlock-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20monospace;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20word-break:\x20break-all;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20display:\x20none;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status.success\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-testing-iconPassed);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status.error\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-testing-iconFailed);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20label\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20display:\x20block;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-weight:\x20500;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20input\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20width:\x20100%;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x206px\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20var(--vscode-input-border);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-input-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-input-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20box-sizing:\x20border-box;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.machine-code-info\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2010px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-descriptionForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-top:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x204px\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-textCodeBlock-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20monospace;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button.secondary\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-secondaryBackground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-button-secondaryForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button.secondary:hover\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-secondaryHoverBackground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20/*\x20心跳/到期相关样式已移除\x20*/\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.user-info\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2011px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-descriptionForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x206px\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-textCodeBlock-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x203px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-left:\x203px\x20solid\x20var(--vscode-textLink-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20</style>\x0a</head>\x0a<body>\x0a\x20\x20\x20\x20<div\x20id=\x22status\x22\x20class=\x22status\x22></div>\x0a\x0a\x20\x20\x20\x20<!--\x20用户验证界面\x20-->\x0a\x20\x20\x20\x20<div\x20id=\x22loginSection\x22\x20class=\x22section\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section-title\x22>🔐\x20用户验证</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20style=\x22font-size:\x2011px;\x20color:\x20var(--vscode-descriptionForeground);\x20margin-bottom:\x2012px;\x20padding:\x206px\x208px;\x20background-color:\x20var(--vscode-textCodeBlock-background);\x20border-radius:\x203px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20🎓\x20本插件用于Augment\x20Code学习研究<br>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20📚\x20学习交流QQ群：1017212982\x20|\x20⚠️\x20仅供学习使用\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22form-group\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22userTokenInput\x22>请输入用户登录Token:</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<input\x20type=\x22text\x22\x20id=\x22userTokenInput\x22\x20placeholder=\x22请输入您的用户登录Token\x22\x20/>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x22\x20onclick=\x22verifyUser()\x22>🔑\x20验证用户</button>\x0a\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20<!--\x20主功能界面\x20(验证通过后显示)\x20-->\x0a\x20\x20\x20\x20<div\x20id=\x22mainSection\x22\x20style=\x22display:\x20none;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<!--\x20账户信息\x20-->\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section-title\x22>👤\x20账户信息</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20id=\x22userEmail\x22\x20class=\x22user-info\x22>正在加载账户信息...</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<!--\x20快速操作\x20-->\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section-title\x22>⚡\x20快速操作</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x22\x20onclick=\x22quickUpdate()\x22>🚀\x20一键更新Token</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<!--\x20当前Token信息\x20-->\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section-title\x22>📋\x20当前Token信息</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20id=\x22currentTokenInfo\x22\x20class=\x22token-info\x22>请使用一键更新Token功能</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x22\x20onclick=\x22updateMachineCode()\x22>🔧\x20更新机器码</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<!--\x20心跳/到期相关区块已移除\x20-->\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<!--\x20登出按钮\x20-->\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22section\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x20secondary\x22\x20onclick=\x22logout()\x22>🚪\x20退出登录</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20style=\x22font-size:\x2010px;\x20color:\x20var(--vscode-descriptionForeground);\x20margin-top:\x2012px;\x20text-align:\x20center;\x20padding:\x204px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20🎓\x20Augment\x20Code学习插件\x20|\x20QQ群：1017212982\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20</div>\x0a\x0a\x0a\x0a\x0a\x0a\x20\x20\x20\x20<script>\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20vscode\x20=\x20acquireVsCodeApi();\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20showStatus(message,\x20type\x20=\x20\x27success\x27)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20statusEl\x20=\x20document.getElementById(\x27status\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.textContent\x20=\x20message;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.className\x20=\x20`status\x20${type}`;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.style.display\x20=\x20\x27block\x27;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20setTimeout(()\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.style.display\x20=\x20\x27none\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20},\x203000);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20verifyUser()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20userToken\x20=\x20document.getElementById(\x27userTokenInput\x27).value.trim();\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(!userToken)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27请输入用户登录Token\x27,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27正在验证用户...\x27,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20command:\x20\x27verifyUser\x27,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20data:\x20{\x20userToken\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20logout()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27loginSection\x27).style.display\x20=\x20\x27block\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27mainSection\x27).style.display\x20=\x20\x27none\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20userTokenInput\x20=\x20document.getElementById(\x27userTokenInput\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(userTokenInput)\x20userTokenInput.value\x20=\x20\x27\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27正在退出登录...\x27,\x20\x27success\x27);\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20通知后端退出登录并清除保存的卡密\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27logout\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20quickUpdate()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27quickUpdate\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20updateMachineCode()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27updateMachineCode\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20页面加载时检查是否有保存的用户Token\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20loadMachineCode()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27checkSavedUserToken\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x0a\x0a\x0a\x0a\x0a\x0a\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20window.addEventListener(\x27message\x27,\x20event\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20message\x20=\x20event.data;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20switch\x20(message.command)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27loading\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27success\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27error\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27verifySuccess\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27用户验证成功！\x27,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27loginSection\x27).style.display\x20=\x20\x27none\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27mainSection\x27).style.display\x20=\x20\x27block\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(message.data\x20&&\x20message.data.user\x20&&\x20message.data.user.email)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20userEmailEl\x20=\x20document.getElementById(\x27userEmail\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(userEmailEl)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20userEmailEl.textContent\x20=\x20\x27邮箱:\x20\x27\x20+\x20message.data.user.email;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27verifyFailed\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message\x20||\x20\x27用户验证失败\x27,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27machineCode\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27machineCodeDisplay\x27).textContent\x20=\x20\x27机器码:\x20\x27\x20+\x20message.data.machineCode;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27savedUserTokenFound\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20找到保存的用户Token，自动触发验证\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27正在使用已保存的Token进行验证...\x27,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27noSavedUserToken\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20没有保存的用户Token，正常显示登录界面\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27logoutSuccess\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20心跳/到期/强制登出相关已移除\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20});\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20页面加载时获取机器码\x0a\x20\x20\x20\x20\x20\x20\x20\x20setTimeout(()\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20loadMachineCode();\x0a\x20\x20\x20\x20\x20\x20\x20\x20},\x20100);\x0a\x0a\x0a\x20\x20\x20\x20</script>\x0a</body>\x0a</html>', 'verifyUser', 'sessionId', '/api/user/available-tokens', 'set', 'tenant_url', 'stopHeartbeat', 'API\x20connection\x20successful!', 'pathname', '482860FoGnsh', 'getAllSettings', 'message', 'loadCurrentSettings', 'getAvailableTokensWithHttp', 'defaultTenantUrl', '🔑\x20Token\x20Manager\x20Settings', 'updateSettings', '<!DOCTYPE\x20html>\x0a<html\x20lang=\x22en\x22>\x0a<head>\x0a\x20\x20\x20\x20<meta\x20charset=\x22UTF-8\x22>\x0a\x20\x20\x20\x20<meta\x20name=\x22viewport\x22\x20content=\x22width=device-width,\x20initial-scale=1.0\x22>\x0a\x20\x20\x20\x20<meta\x20http-equiv=\x22Content-Security-Policy\x22\x20content=\x22default-src\x20\x27none\x27;\x20style-src\x20\x27unsafe-inline\x27;\x20script-src\x20\x27unsafe-inline\x27;\x22>\x0a\x20\x20\x20\x20<title>Token\x20Manager\x20Settings</title>\x0a\x20\x20\x20\x20<style>\x0a\x20\x20\x20\x20\x20\x20\x20\x20body\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-family:\x20-apple-system,\x20BlinkMacSystemFont,\x20\x27Segoe\x20UI\x27,\x20Roboto,\x20sans-serif;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin:\x200;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2020px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background:\x20linear-gradient(135deg,\x20var(--vscode-editor-background)\x200%,\x20var(--vscode-sideBar-background)\x20100%);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-editor-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20min-height:\x20100vh;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.container\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20max-width:\x20800px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin:\x200\x20auto;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.header\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20text-align:\x20center;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2030px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2020px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background:\x20var(--vscode-editor-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20var(--vscode-panel-border);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.header\x20h1\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin:\x200\x200\x2010px\x200;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2024px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-textLink-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.card\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background:\x20var(--vscode-editor-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x208px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2020px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2020px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20var(--vscode-panel-border);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.card-title\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-weight:\x20600;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2015px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-textLink-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2016px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2015px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20label\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20display:\x20block;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x205px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-weight:\x20500;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20input\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20width:\x20100%;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20var(--vscode-input-border);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-input-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-input-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20box-sizing:\x20border-box;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.form-group\x20.description\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-top:\x205px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-descriptionForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-background);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-button-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x20none;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px\x2016px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20cursor:\x20pointer;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-right:\x2010px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2010px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button:hover\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-hoverBackground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button.secondary\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-button-secondaryBackground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20var(--vscode-button-secondaryForeground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.button.danger\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20#dc3545;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x2010px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-bottom:\x2015px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20display:\x20none;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status.success\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20#28a745;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status.error\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20#dc3545;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.status.info\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20var(--vscode-textLink-foreground);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20white;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.test-result\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20margin-top:\x2010px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20padding:\x208px\x2012px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border-radius:\x204px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20font-size:\x2013px;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20display:\x20none;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.test-result.success\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20rgba(40,\x20167,\x2069,\x200.1);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20#28a745;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20rgba(40,\x20167,\x2069,\x200.3);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20.test-result.error\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20background-color:\x20rgba(220,\x2053,\x2069,\x200.1);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20color:\x20#dc3545;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20border:\x201px\x20solid\x20rgba(220,\x2053,\x2069,\x200.3);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20</style>\x0a</head>\x0a<body>\x0a\x20\x20\x20\x20<div\x20class=\x22container\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22header\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<h1>🔑\x20Token管理器设置</h1>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p>配置API连接和默认参数</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<p\x20style=\x22font-size:\x2012px;\x20color:\x20var(--vscode-descriptionForeground);\x20margin-top:\x2010px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20🎓\x20本插件用于Augment\x20Code学习研究\x20|\x20📚\x20学习交流QQ群：1017212982\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</p>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20id=\x22status\x22\x20class=\x22status\x22></div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22card\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22card-title\x22>🌐\x20API配置</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22form-group\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22apiBaseUrl\x22>API基础URL:</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<input\x20type=\x22text\x22\x20id=\x22apiBaseUrl\x22\x20placeholder=\x22', 'onDidDispose', 'updateSessionsData', 'html', '/api/user/verify', 'forceLogout', '\x22\x20/>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22description\x22>用于获取令牌的API基础URL（使用固定API\x20Key认证）</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x20secondary\x22\x20onclick=\x22testConnection()\x22>🔗\x20测试API连接</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20id=\x22testResult\x22\x20class=\x22test-result\x22></div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22card\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22card-title\x22>🏠\x20默认配置</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22form-group\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<label\x20for=\x22defaultTenantUrl\x22>默认租户URL:</label>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<input\x20type=\x22text\x22\x20id=\x22defaultTenantUrl\x22\x20placeholder=\x22https://d5.api.augmentcode.com/\x22\x20/>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<div\x20class=\x22description\x22>未指定特定URL时使用的默认租户URL</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20<div\x20style=\x22display:\x20flex;\x20gap:\x2010px;\x20margin-top:\x2020px;\x22>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x22\x20onclick=\x22saveSettings()\x22>💾\x20保存设置</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x20secondary\x22\x20onclick=\x22loadSettings()\x22>🔄\x20重新加载</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20<button\x20class=\x22button\x20danger\x22\x20onclick=\x22resetSettings()\x22>🗑️\x20重置为默认</button>\x0a\x20\x20\x20\x20\x20\x20\x20\x20</div>\x0a\x20\x20\x20\x20</div>\x0a\x0a\x20\x20\x20\x20<script>\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20vscode\x20=\x20acquireVsCodeApi();\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20showStatus(message,\x20type\x20=\x20\x27info\x27)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20statusEl\x20=\x20document.getElementById(\x27status\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.textContent\x20=\x20message;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.className\x20=\x20`status\x20${type}`;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.style.display\x20=\x20\x27block\x27;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(type\x20!==\x20\x27error\x27)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20setTimeout(()\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20statusEl.style.display\x20=\x20\x27none\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20},\x203000);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20loadSettings()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27loadSettings\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20saveSettings()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20settings\x20=\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20apiBaseUrl:\x20document.getElementById(\x27apiBaseUrl\x27).value.trim(),\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20defaultTenantUrl:\x20document.getElementById(\x27defaultTenantUrl\x27).value.trim()\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20移除apiAuthToken，现在使用固定的X-API-Key\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20};\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(!settings.apiBaseUrl)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27请输入API基础URL\x27,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(!settings.defaultTenantUrl)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(\x27请输入默认租户URL\x27,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20command:\x20\x27saveSettings\x27,\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20data:\x20settings\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20resetSettings()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20(confirm(\x27确定要将所有设置重置为默认值吗？此操作无法撤销。\x27))\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27resetSettings\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20testConnection()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20testResult\x20=\x20document.getElementById(\x27testResult\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20testResult.style.display\x20=\x20\x27none\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vscode.postMessage({\x20command:\x20\x27testApiConnection\x27\x20});\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20window.addEventListener(\x27message\x27,\x20event\x20=>\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20message\x20=\x20event.data;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20switch\x20(message.command)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27loading\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27info\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27error\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27error\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27success\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20showStatus(message.data.message,\x20\x27success\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27settingsLoaded\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20displaySettings(message.data);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20case\x20\x27connectionTestResult\x27:\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20displayTestResult(message.data);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20break;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20});\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20displaySettings(settings)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27apiBaseUrl\x27).value\x20=\x20settings.apiBaseUrl\x20||\x20\x27\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20document.getElementById(\x27defaultTenantUrl\x27).value\x20=\x20settings.defaultTenantUrl\x20||\x20\x27\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20移除apiAuthToken显示，现在使用固定的X-API-Key\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20function\x20displayTestResult(result)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20testResult\x20=\x20document.getElementById(\x27testResult\x27);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20testResult.textContent\x20=\x20result.message;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20testResult.className\x20=\x20`test-result\x20${result.success\x20?\x20\x27success\x27\x20:\x20\x27error\x27}`;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20testResult.style.display\x20=\x20\x27block\x27;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20loadSettings();\x0a\x20\x20\x20\x20</script>\x0a</body>\x0a</html>', 'One', 'vscode', 'window', 'application/json', 'email', 'testApiConnection', 'Failed\x20to\x20reset\x20settings', 'toISOString', 'Settings\x20saved\x20successfully!', 'accessToken', 'http', 'heartbeatTimer', 'extensionUri', '退出登录时发生错误', 'loadSettings', 'generateCardMachineCode'];
  _0x1374 = function () {
    return _0xa1f324;
  };
  return _0x1374();
}

function _0x2a3b(_0x418c60, _0x557855) {
  const _0x1374f4 = _0x1374();
  return _0x2a3b = function (_0x2a3b1b, _0xdfaf3d) {
    _0x2a3b1b = _0x2a3b1b - 0x1c9;
    let _0x338f1a = _0x1374f4[_0x2a3b1b];
    return _0x338f1a;
  }, _0x2a3b(_0x418c60, _0x557855);
} (function (_0x407d67, _0x2c4464) {
  const _0x346ca5 = _0x2a3b,
    _0x54ba42 = _0x407d67();
  while (!![]) {
    try {
      const _0x270de5 = parseInt(_0x346ca5(0x216)) / 0x1 * (parseInt(_0x346ca5(0x1cb)) / 0x2) + parseInt(_0x346ca5(0x1f7)) / 0x3 + -parseInt(_0x346ca5(0x224)) / 0x4 * (-parseInt(_0x346ca5(0x259)) / 0x5) + -parseInt(_0x346ca5(0x251)) / 0x6 + parseInt(_0x346ca5(0x26c)) / 0x7 + parseInt(_0x346ca5(0x258)) / 0x8 * (parseInt(_0x346ca5(0x20e)) / 0x9) + -parseInt(_0x346ca5(0x218)) / 0xa;
      if (_0x270de5 === _0x2c4464) break;
      else _0x54ba42['push'](_0x54ba42['shift']());
    } catch (_0x3520d9) {
      _0x54ba42['push'](_0x54ba42['shift']());
    }
  }
}(_0x1374, 0x27cef));
const vscode = require(_0x592fd3(0x27c)),
  https = require(_0x592fd3(0x22a)),
  http = require(_0x592fd3(0x285)),
  {
    URL
  } = require(_0x592fd3(0x1f6)),
  DEFAULT_API_BASE_URL = _0x592fd3(0x1f1);
class SimpleTokenApiService {
  [_0x592fd3(0x255)]() {
    const _0x201e9d = _0x592fd3,
      _0x3c2b23 = vscode[_0x201e9d(0x1db)][_0x201e9d(0x23b)](_0x201e9d(0x238));
    return _0x3c2b23[_0x201e9d(0x1d4)](_0x201e9d(0x21c), DEFAULT_API_BASE_URL);
  }
  async ['getAvailableTokens'](_0x1febc8 = 0x1, _0x3274d5 = 0x32, _0x1d6d5b) {
    const _0x4daa96 = _0x592fd3;
    try {
      const _0x1e84b5 = this[_0x4daa96(0x255)]();
      if (!_0x1d6d5b) throw new Error(_0x4daa96(0x1d5));
      const _0x5d6a22 = new URL('/api/user/available-tokens', _0x1e84b5);
      _0x5d6a22[_0x4daa96(0x20a)][_0x4daa96(0x267)]('page', _0x1febc8[_0x4daa96(0x210)]()), _0x5d6a22[_0x4daa96(0x20a)][_0x4daa96(0x267)](_0x4daa96(0x21e), _0x3274d5[_0x4daa96(0x210)]());
      const _0x3e1ec9 = {
        'Content-Type': _0x4daa96(0x27e),
        'User-Agent': _0x4daa96(0x1cc),
        'Authorization': _0x4daa96(0x1e0) + _0x1d6d5b
      },
        _0xbd0b08 = await fetch(_0x5d6a22[_0x4daa96(0x210)](), {
          'method': _0x4daa96(0x233),
          'headers': _0x3e1ec9,
          'signal': AbortSignal[_0x4daa96(0x24a)](0x7530)
        });
      if (!_0xbd0b08['ok']) throw new Error(_0x4daa96(0x1eb) + _0xbd0b08['status'] + ':\x20' + _0xbd0b08[_0x4daa96(0x1e9)]);
      const _0x52e59d = await _0xbd0b08[_0x4daa96(0x248)]();
      if (_0x52e59d && _0x52e59d[_0x4daa96(0x1dd)] && Array[_0x4daa96(0x1d1)](_0x52e59d[_0x4daa96(0x1f0)])) {
        const _0x210e50 = _0x52e59d[_0x4daa96(0x1f0)][_0x4daa96(0x207)](_0x1ab138 => ({
          'id': _0x1ab138['id'],
          'accessToken': _0x1ab138[_0x4daa96(0x202)],
          'tenantURL': _0x1ab138[_0x4daa96(0x268)],
          'useTime': _0x1ab138['use_time'],
          'createdAt': _0x1ab138[_0x4daa96(0x24e)],
          'updatedAt': _0x1ab138[_0x4daa96(0x21b)]
        }));
        return _0x210e50;
      } else throw new Error(_0x4daa96(0x22f));
    } catch (_0x527fd3) {
      if (_0x527fd3[_0x4daa96(0x26e)][_0x4daa96(0x237)](_0x4daa96(0x25d))) return await this[_0x4daa96(0x270)](_0x1febc8, _0x3274d5, _0x1d6d5b);
      throw new Error(_0x4daa96(0x22e) + _0x527fd3[_0x4daa96(0x26e)]);
    }
  }
  async [_0x592fd3(0x270)](_0x4935b0 = 0x1, _0x3c8bd1 = 0x32, _0x5bae62) {
    return new Promise((_0x1eee28, _0x4762b9) => {
      const _0x1bd784 = _0x2a3b;
      try {
        const _0x175cdc = this[_0x1bd784(0x255)]();
        if (!_0x5bae62) {
          _0x4762b9(new Error(_0x1bd784(0x1d5)));
          return;
        }
        const _0x33ce53 = new URL(_0x1bd784(0x266), _0x175cdc);
        _0x33ce53[_0x1bd784(0x20a)][_0x1bd784(0x267)]('page', _0x4935b0[_0x1bd784(0x210)]()), _0x33ce53[_0x1bd784(0x20a)][_0x1bd784(0x267)]('limit', _0x3c8bd1[_0x1bd784(0x210)]());
        const _0x3f36bf = _0x33ce53[_0x1bd784(0x257)] === _0x1bd784(0x262),
          _0x54511b = _0x3f36bf ? https : http,
          _0x4032e2 = {
            'hostname': _0x33ce53[_0x1bd784(0x1e5)],
            'port': _0x33ce53['port'] || (_0x3f36bf ? 0x1bb : 0x50),
            'path': _0x33ce53[_0x1bd784(0x26b)] + _0x33ce53[_0x1bd784(0x23f)],
            'method': _0x1bd784(0x233),
            'headers': {
              'Content-Type': 'application/json',
              'User-Agent': _0x1bd784(0x1cc),
              'Authorization': _0x1bd784(0x1e0) + _0x5bae62
            },
            'timeout': 0x7530
          },
          _0x546cf1 = _0x54511b[_0x1bd784(0x1e7)](_0x4032e2, _0x3628d5 => {
            const _0x1ec06a = _0x1bd784;
            let _0x30a16f = '';
            _0x3628d5['on'](_0x1ec06a(0x1e6), _0x3b92a6 => {
              _0x30a16f += _0x3b92a6;
            }), _0x3628d5['on'](_0x1ec06a(0x229), () => {
              const _0x33536f = _0x1ec06a;
              try {
                if (_0x3628d5[_0x33536f(0x203)] >= 0xc8 && _0x3628d5[_0x33536f(0x203)] < 0x12c) {
                  const _0x4292dc = JSON['parse'](_0x30a16f);
                  if (_0x4292dc && _0x4292dc[_0x33536f(0x1dd)] && Array['isArray'](_0x4292dc[_0x33536f(0x1f0)])) {
                    const _0x1c52ba = _0x4292dc[_0x33536f(0x1f0)]['map'](_0x7e1d16 => ({
                      'id': _0x7e1d16['id'],
                      'accessToken': _0x7e1d16[_0x33536f(0x202)],
                      'tenantURL': _0x7e1d16[_0x33536f(0x268)],
                      'useTime': _0x7e1d16[_0x33536f(0x228)],
                      'createdAt': _0x7e1d16[_0x33536f(0x24e)],
                      'updatedAt': _0x7e1d16[_0x33536f(0x21b)]
                    }));
                    _0x1eee28(_0x1c52ba);
                  } else _0x4762b9(new Error(_0x33536f(0x22f)));
                } else _0x4762b9(new Error(_0x33536f(0x1eb) + _0x3628d5[_0x33536f(0x203)] + ':\x20' + _0x3628d5[_0x33536f(0x219)]));
              } catch (_0x4c16b4) {
                _0x4762b9(new Error(_0x33536f(0x208)));
              }
            });
          });
        _0x546cf1['on']('error', _0x478143 => {
          _0x4762b9(_0x478143);
        }), _0x546cf1['on'](_0x1bd784(0x24a), () => {
          const _0x4015d9 = _0x1bd784;
          _0x546cf1[_0x4015d9(0x247)](), _0x4762b9(new Error(_0x4015d9(0x253)));
        }), _0x546cf1[_0x1bd784(0x229)]();
      } catch (_0x490820) {
        _0x4762b9(_0x490820);
      }
    });
  }
}
class SimpleSettingsManager {
  ['getAllSettings']() {
    const _0x325fd5 = _0x592fd3,
      _0x808b11 = vscode[_0x325fd5(0x1db)][_0x325fd5(0x23b)]('augmentTokenManager');
    return {
      'apiBaseUrl': _0x808b11[_0x325fd5(0x1d4)](_0x325fd5(0x21c), DEFAULT_API_BASE_URL),
      'defaultTenantUrl': _0x808b11['get'](_0x325fd5(0x271), _0x325fd5(0x25a))
    };
  }
  async [_0x592fd3(0x273)](_0x15ca89, _0x5944bb = !![]) {
    const _0x5c754a = _0x592fd3;
    try {
      const _0x270b17 = vscode[_0x5c754a(0x1db)][_0x5c754a(0x23b)](_0x5c754a(0x238));
      for (const [_0xeb8481, _0x28b1fb] of Object['entries'](_0x15ca89)) {
        await _0x270b17[_0x5c754a(0x20c)](_0xeb8481, _0x28b1fb, _0x5944bb);
      }
      return !![];
    } catch (_0x132d49) {
      return ![];
    }
  } [_0x592fd3(0x20d)](_0x29e7ff) {
    const _0x5a2475 = _0x592fd3,
      _0x421e2b = [];
    return !_0x29e7ff[_0x5a2475(0x21c)] && _0x421e2b[_0x5a2475(0x217)](_0x5a2475(0x21d)), !_0x29e7ff[_0x5a2475(0x271)] && _0x421e2b[_0x5a2475(0x217)](_0x5a2475(0x223)), {
      'valid': _0x421e2b[_0x5a2475(0x211)] === 0x0,
      'errors': _0x421e2b
    };
  }
  async [_0x592fd3(0x261)](_0x4f4e03 = !![]) {
    const _0x54c345 = _0x592fd3;
    try {
      const _0x54a3d4 = {
        'apiBaseUrl': DEFAULT_API_BASE_URL,
        'defaultTenantUrl': _0x54c345(0x25a)
      };
      return await this[_0x54c345(0x273)](_0x54a3d4, _0x4f4e03);
    } catch (_0x28ecf7) {
      return ![];
    }
  }
}
class SimpleSettingsWebViewProvider {
  constructor(_0x210e70) {
    const _0x48bbf9 = _0x592fd3;
    this[_0x48bbf9(0x235)] = _0x210e70, this[_0x48bbf9(0x245)] = null, this['settingsManager'] = new SimpleSettingsManager();
  } [_0x592fd3(0x206)]() {
    const _0x250782 = _0x592fd3,
      _0xd4cb55 = vscode['window'][_0x250782(0x239)] ? vscode[_0x250782(0x27d)][_0x250782(0x239)][_0x250782(0x24b)] : undefined;
    if (this[_0x250782(0x245)]) {
      this[_0x250782(0x245)]['reveal'](_0xd4cb55);
      return;
    }
    this[_0x250782(0x245)] = vscode[_0x250782(0x27d)][_0x250782(0x215)](_0x250782(0x254), _0x250782(0x272), _0xd4cb55 || vscode[_0x250782(0x214)][_0x250782(0x27b)], {
      'enableScripts': !![],
      'retainContextWhenHidden': !![],
      'localResourceRoots': [this[_0x250782(0x235)][_0x250782(0x287)]]
    }), this[_0x250782(0x245)][_0x250782(0x23e)]['html'] = this['getWebViewContent'](), this[_0x250782(0x245)][_0x250782(0x23e)][_0x250782(0x1ef)](async _0x587f58 => {
      const _0x274a58 = _0x250782;
      await this[_0x274a58(0x22d)](_0x587f58);
    }, undefined, this[_0x250782(0x235)][_0x250782(0x23d)]), this[_0x250782(0x245)][_0x250782(0x275)](() => {
      this['panel'] = null;
    }, null, this['context']['subscriptions']), setTimeout(() => {
      const _0x5f86bb = _0x250782;
      this[_0x5f86bb(0x26f)]();
    }, 0x64);
  }
  async [_0x592fd3(0x22d)](_0x4cee99) {
    const _0x3da983 = _0x592fd3;
    try {
      switch (_0x4cee99[_0x3da983(0x1ec)]) {
        case _0x3da983(0x289):
          await this[_0x3da983(0x26f)]();
          break;
        case _0x3da983(0x232):
          await this[_0x3da983(0x232)](_0x4cee99[_0x3da983(0x1e6)]);
          break;
        case _0x3da983(0x230):
          await this[_0x3da983(0x230)]();
          break;
        case _0x3da983(0x280):
          await this['testApiConnection']();
          break;
      }
    } catch (_0x56e84d) {
      this[_0x3da983(0x1f8)]({
        'command': 'error',
        'data': {
          'message': _0x56e84d['message']
        }
      });
    }
  }
  async [_0x592fd3(0x26f)]() {
    const _0x2600c9 = _0x592fd3;
    try {
      const _0x580379 = this[_0x2600c9(0x226)][_0x2600c9(0x26d)]();
      this[_0x2600c9(0x1f8)]({
        'command': _0x2600c9(0x204),
        'data': _0x580379
      });
    } catch (_0x36f8fa) {
      this[_0x2600c9(0x1f8)]({
        'command': _0x2600c9(0x1e2),
        'data': {
          'message': _0x2600c9(0x24d) + _0x36f8fa[_0x2600c9(0x26e)]
        }
      });
    }
  }
  async [_0x592fd3(0x232)](_0x44f24c) {
    const _0x1162c4 = _0x592fd3;
    try {
      const _0x1cd0ca = this[_0x1162c4(0x226)][_0x1162c4(0x20d)](_0x44f24c);
      if (!_0x1cd0ca['valid']) throw new Error(_0x1162c4(0x21a) + _0x1cd0ca[_0x1162c4(0x25c)]['join'](',\x20'));
      const _0x59fd6d = await this[_0x1162c4(0x226)][_0x1162c4(0x273)](_0x44f24c, !![]);
      if (_0x59fd6d) this[_0x1162c4(0x1f8)]({
        'command': _0x1162c4(0x1dd),
        'data': {
          'message': _0x1162c4(0x283)
        }
      }), setTimeout(() => {
        this['loadCurrentSettings']();
      }, 0x1f4);
      else throw new Error(_0x1162c4(0x23a));
    } catch (_0x1a69aa) {
      this[_0x1162c4(0x1f8)]({
        'command': 'error',
        'data': {
          'message': 'Failed\x20to\x20save\x20settings:\x20' + _0x1a69aa[_0x1162c4(0x26e)]
        }
      });
    }
  }
  async [_0x592fd3(0x230)]() {
    const _0x54013e = _0x592fd3;
    try {
      const _0x156aad = await this[_0x54013e(0x226)][_0x54013e(0x261)](!![]);
      if (_0x156aad) this['sendMessage']({
        'command': _0x54013e(0x1dd),
        'data': {
          'message': _0x54013e(0x220)
        }
      }), setTimeout(() => {
        const _0x2de36f = _0x54013e;
        this[_0x2de36f(0x26f)]();
      }, 0x1f4);
      else throw new Error(_0x54013e(0x281));
    } catch (_0x5d46fe) {
      this[_0x54013e(0x1f8)]({
        'command': _0x54013e(0x1e2),
        'data': {
          'message': 'Failed\x20to\x20reset\x20settings:\x20' + _0x5d46fe['message']
        }
      });
    }
  }
  async [_0x592fd3(0x280)]() {
    const _0x553508 = _0x592fd3;
    try {
      const _0x4a844a = new SimpleTokenApiService(),
        _0x313060 = await _0x4a844a[_0x553508(0x205)]();
      this[_0x553508(0x1f8)]({
        'command': _0x553508(0x212),
        'data': {
          'success': _0x313060,
          'message': _0x313060 ? _0x553508(0x26a) : 'API\x20connection\x20failed'
        }
      });
    } catch (_0x2a87db) {
      this['sendMessage']({
        'command': 'error',
        'data': {
          'message': 'Connection\x20test\x20failed:\x20' + _0x2a87db[_0x553508(0x26e)]
        }
      });
    }
  } ['sendMessage'](_0x2f6a15) {
    const _0x228add = _0x592fd3;
    this[_0x228add(0x245)] && this[_0x228add(0x245)][_0x228add(0x23e)] && this[_0x228add(0x245)][_0x228add(0x23e)][_0x228add(0x221)](_0x2f6a15);
  } ['getWebViewContent']() {
    const _0x3a439e = _0x592fd3;
    return _0x3a439e(0x274) + DEFAULT_API_BASE_URL + _0x3a439e(0x27a);
  }
}
class SimpleTokenManager {
  constructor(_0x33db12) {
    const _0x2934a3 = _0x592fd3;
    this[_0x2934a3(0x235)] = _0x33db12;
  }
  async ['getAccessToken']() {
    const _0x4770f3 = _0x592fd3;
    try {
      const _0x1a3a66 = await this[_0x4770f3(0x235)][_0x4770f3(0x1f9)]['get'](_0x4770f3(0x201));
      if (_0x1a3a66) {
        const _0x31318f = JSON[_0x4770f3(0x20f)](_0x1a3a66);
        return {
          'success': !![],
          'accessToken': _0x31318f[_0x4770f3(0x284)],
          'tenantURL': _0x31318f['tenantURL'],
          'data': _0x31318f
        };
      } else return {
        'success': ![],
        'error': _0x4770f3(0x1fc)
      };
    } catch (_0x5922ed) {
      return {
        'success': ![],
        'error': _0x5922ed[_0x4770f3(0x26e)]
      };
    }
  }
  async [_0x592fd3(0x276)](_0x418fc4, _0x7b3057) {
    const _0x7b6084 = _0x592fd3;
    try {
      const _0x39a226 = {
        'tenantURL': _0x418fc4,
        'accessToken': _0x7b3057,
        'scopes': [_0x7b6084(0x27f)]
      };
      return await this[_0x7b6084(0x235)]['secrets'][_0x7b6084(0x21f)](_0x7b6084(0x201), JSON[_0x7b6084(0x24c)](_0x39a226)), {
        'success': !![],
        'data': _0x39a226
      };
    } catch (_0x40a58d) {
      return {
        'success': ![],
        'error': _0x40a58d['message']
      };
    }
  }
  async ['getCardMachineCode']() {
    const _0xd63ffd = _0x592fd3;
    try {
      let _0x532a8d = await this['context'][_0xd63ffd(0x1da)][_0xd63ffd(0x1d4)](_0xd63ffd(0x22c));
      return !_0x532a8d && (_0x532a8d = this[_0xd63ffd(0x28a)](), await this[_0xd63ffd(0x235)][_0xd63ffd(0x1da)][_0xd63ffd(0x20c)](_0xd63ffd(0x22c), _0x532a8d)), _0x532a8d;
    } catch (_0x4f244e) {
      return this['generateCardMachineCode']();
    }
  } ['generateCardMachineCode']() {
    const _0x26ebe7 = _0x592fd3,
      _0x5296f3 = _0x26ebe7(0x1ed);
    let _0x4d7f5c = '';
    for (let _0x519b0a = 0x0; _0x519b0a < 0x20; _0x519b0a++) {
      _0x4d7f5c += _0x5296f3['charAt'](Math[_0x26ebe7(0x1ff)](Math[_0x26ebe7(0x1fd)]() * _0x5296f3[_0x26ebe7(0x211)]));
    }
    return _0x4d7f5c;
  }
}
class SimpleTokenManagerSidebarProvider {
  constructor(_0x1a73fe, _0x552960, _0x1da761) {
    const _0x59ca9c = _0x592fd3;
    this[_0x59ca9c(0x235)] = _0x1a73fe, this['tokenManager'] = _0x552960, this['apiService'] = _0x1da761, this[_0x59ca9c(0x1f4)] = undefined, this[_0x59ca9c(0x286)] = null, this[_0x59ca9c(0x1d2)] = null;
  } [_0x592fd3(0x1fa)](_0x49f8eb, _0x2e8cef, _0x2e0003) {
    const _0x3e820e = _0x592fd3;
    this[_0x3e820e(0x1f4)] = _0x49f8eb, _0x49f8eb[_0x3e820e(0x23e)]['options'] = {
      'enableScripts': !![],
      'localResourceRoots': [this['context'][_0x3e820e(0x287)]]
    }, _0x49f8eb[_0x3e820e(0x23e)][_0x3e820e(0x277)] = this[_0x3e820e(0x1df)](), _0x49f8eb['webview'][_0x3e820e(0x1ef)](async _0x168185 => {
      await this['handleMessage'](_0x168185);
    }, undefined, this[_0x3e820e(0x235)]['subscriptions']);
  }
  async ['handleMessage'](_0x427873) {
    const _0x460f5 = _0x592fd3;
    try {
      switch (_0x427873[_0x460f5(0x1ec)]) {
        case _0x460f5(0x264):
          await this[_0x460f5(0x264)](_0x427873[_0x460f5(0x1e6)]);
          break;
        case _0x460f5(0x1d8):
          await this[_0x460f5(0x1d8)]();
          break;
        case _0x460f5(0x25f):
          await this['logout']();
          break;
        case _0x460f5(0x1e1):
          await this[_0x460f5(0x1e1)]();
          break;
        case _0x460f5(0x25e):
          await this[_0x460f5(0x25e)]();
          break;
        case 'stopHeartbeat':
          this[_0x460f5(0x269)]();
          break;
        default:
      }
    } catch (_0x297929) {
      this[_0x460f5(0x1f8)]({
        'command': _0x460f5(0x1e2),
        'data': {
          'message': _0x297929['message']
        }
      });
    }
  }
  async [_0x592fd3(0x264)](_0x39c6f3) {
    const _0xb7689f = _0x592fd3;
    try {
      const {
        userToken: _0xe82565
      } = _0x39c6f3 || {};
      if (!_0xe82565) throw new Error(_0xb7689f(0x1ee));
      this[_0xb7689f(0x1f8)]({
        'command': _0xb7689f(0x24f),
        'data': {
          'message': '正在验证用户...'
        }
      });
      const _0x3be6bc = await this[_0xb7689f(0x240)](_0xe82565);
      _0x3be6bc[_0xb7689f(0x1dd)] && _0x3be6bc[_0xb7689f(0x1fb)] ? (this[_0xb7689f(0x1d2)] = {
        'userToken': _0xe82565,
        'user': _0x3be6bc[_0xb7689f(0x246)],
        'verifiedAt': _0x3be6bc[_0xb7689f(0x1ca)] || new Date()[_0xb7689f(0x282)]()
      }, await this[_0xb7689f(0x1cf)][_0xb7689f(0x235)][_0xb7689f(0x1da)][_0xb7689f(0x20c)]('savedUserToken', _0xe82565), this[_0xb7689f(0x1f8)]({
        'command': _0xb7689f(0x1fe),
        'data': {
          'user': _0x3be6bc[_0xb7689f(0x246)]
        }
      })) : this['sendMessage']({
        'command': _0xb7689f(0x249),
        'data': {
          'message': _0x3be6bc[_0xb7689f(0x1e2)] || _0xb7689f(0x260)
        }
      });
    } catch (_0x134ef4) {
      this[_0xb7689f(0x1f8)]({
        'command': _0xb7689f(0x249),
        'data': {
          'message': _0xb7689f(0x20b) + _0x134ef4[_0xb7689f(0x26e)]
        }
      });
    }
  }
  async [_0x592fd3(0x250)]() {
    const _0x2d52ad = _0x592fd3;
    try {
      const _0x46c383 = await this[_0x2d52ad(0x1cf)][_0x2d52ad(0x1d9)]();
      this[_0x2d52ad(0x1f8)]({
        'command': _0x2d52ad(0x1d6),
        'data': {
          'machineCode': _0x46c383
        }
      });
    } catch (_0x28047a) {
      this[_0x2d52ad(0x1f8)]({
        'command': _0x2d52ad(0x1d6),
        'data': {
          'machineCode': _0x2d52ad(0x1d0)
        }
      });
    }
  }
  async ['checkSavedUserToken']() {
    const _0x566ef8 = _0x592fd3;
    try {
      const _0x8f0983 = await this['tokenManager'][_0x566ef8(0x235)][_0x566ef8(0x1da)][_0x566ef8(0x1d4)](_0x566ef8(0x242));
      _0x8f0983 ? (this[_0x566ef8(0x1f8)]({
        'command': _0x566ef8(0x209)
      }), await this[_0x566ef8(0x264)]({
        'userToken': _0x8f0983
      })) : this['sendMessage']({
        'command': _0x566ef8(0x225)
      });
    } catch (_0x34bebd) {
      this[_0x566ef8(0x1f8)]({
        'command': 'noSavedUserToken'
      });
    }
  }
  async [_0x592fd3(0x25f)]() {
    const _0x566416 = _0x592fd3;
    try {
      this[_0x566416(0x269)](), this['currentUserData'] = null, await this['tokenManager'][_0x566416(0x235)]['globalState'][_0x566416(0x20c)](_0x566416(0x242), undefined), this[_0x566416(0x1f8)]({
        'command': _0x566416(0x227),
        'data': {
          'message': _0x566416(0x243)
        }
      });
    } catch (_0x2e6f65) {
      this[_0x566416(0x1f8)]({
        'command': 'error',
        'data': {
          'message': _0x566416(0x288)
        }
      });
    }
  }
  async [_0x592fd3(0x240)](_0x50df4e) {
    const _0x5a1508 = _0x592fd3;
    try {
      const _0x7b6cf0 = this[_0x5a1508(0x1ce)][_0x5a1508(0x255)]() || DEFAULT_API_BASE_URL,
        _0x3e913 = new URL(_0x5a1508(0x278), _0x7b6cf0),
        _0x5e042b = await fetch(_0x3e913[_0x5a1508(0x210)](), {
          'method': _0x5a1508(0x233),
          'headers': {
            'Authorization': 'Bearer\x20' + _0x50df4e,
            'Content-Type': _0x5a1508(0x27e),
            'User-Agent': _0x5a1508(0x1cc)
          },
          'signal': AbortSignal['timeout'](0x7530)
        }),
        _0x456a87 = await _0x5e042b[_0x5a1508(0x248)]();
      return _0x456a87;
    } catch (_0x224d6c) {
      return {
        'success': ![],
        'error': _0x224d6c[_0x5a1508(0x26e)]
      };
    }
  } [_0x592fd3(0x1f3)]() {
    const _0x2f22ca = _0x592fd3;
    this[_0x2f22ca(0x286)] && (clearInterval(this['heartbeatTimer']), this[_0x2f22ca(0x286)] = null);
    return;
  } ['stopHeartbeat']() {
    const _0x7d0285 = _0x592fd3;
    this[_0x7d0285(0x286)] && (clearInterval(this[_0x7d0285(0x286)]), this[_0x7d0285(0x286)] = null);
    return;
  }
  async [_0x592fd3(0x1dc)]() {
    return;
  }
  async ['callUserStatusApi'](_0x3b3f41, _0x58b863) {
    return {
      'success': !![]
    };
  }
  async ['getExpiryTime']() {
    return;
  }
  async [_0x592fd3(0x1d7)](_0xc43ae7) {
    return {
      'success': ![],
      'error': 'expiry\x20disabled'
    };
  }
  async [_0x592fd3(0x279)](_0x11e20d) {
    return;
  }
  async [_0x592fd3(0x1e1)]() {
    const _0x534eff = _0x592fd3;
    try {
      this[_0x534eff(0x1f8)]({
        'command': _0x534eff(0x24f),
        'data': {
          'message': _0x534eff(0x1e4)
        }
      });
      const _0x3b360b = this[_0x534eff(0x1d2)] && this[_0x534eff(0x1d2)][_0x534eff(0x23c)],
        _0x44b4f0 = await this[_0x534eff(0x1ce)][_0x534eff(0x1cd)](0x1, 0x64, _0x3b360b);
      if (!_0x44b4f0 || _0x44b4f0[_0x534eff(0x211)] === 0x0) throw new Error(_0x534eff(0x1d3));
      const _0x47e477 = Math[_0x534eff(0x1ff)](Math[_0x534eff(0x1fd)]() * _0x44b4f0[_0x534eff(0x211)]),
        _0x14c63d = _0x44b4f0[_0x47e477],
        _0x5c6fa7 = await this['tokenManager'][_0x534eff(0x276)](_0x14c63d[_0x534eff(0x213)], _0x14c63d[_0x534eff(0x284)]);
      if (_0x5c6fa7[_0x534eff(0x1dd)]) {
        if (this[_0x534eff(0x1d2)] && this[_0x534eff(0x1d2)][_0x534eff(0x246)] && this['currentUserData'][_0x534eff(0x246)][_0x534eff(0x27f)]) try {
          const _0x209f2e = await this[_0x534eff(0x1de)](_0x14c63d[_0x534eff(0x284)], this[_0x534eff(0x1d2)]['user'][_0x534eff(0x27f)]);
          if (_0x209f2e[_0x534eff(0x1dd)]) { } else { }
        } catch (_0x44b208) { }
        this[_0x534eff(0x1f8)]({
          'command': 'success',
          'data': {
            'message': _0x534eff(0x1f2) + (_0x47e477 + 0x1) + '个token\x20(共' + _0x44b4f0[_0x534eff(0x211)] + '个)'
          }
        });
      } else throw new Error(_0x5c6fa7[_0x534eff(0x1e2)]);
    } catch (_0x4268d0) {
      this[_0x534eff(0x1f8)]({
        'command': _0x534eff(0x1e2),
        'data': {
          'message': _0x534eff(0x1c9) + _0x4268d0[_0x534eff(0x26e)]
        }
      });
    }
  }
  async [_0x592fd3(0x1de)](_0x45952f, _0x582272) {
    const _0x4242ad = _0x592fd3;
    try {
      const _0x5372d8 = this[_0x4242ad(0x1ce)][_0x4242ad(0x255)](),
        _0x5be3c8 = new URL(_0x4242ad(0x244), _0x5372d8),
        _0x1a2acb = {
          'token': _0x45952f,
          'user_ck': _0x582272
        },
        _0x5cc6b1 = {
          'Content-Type': _0x4242ad(0x27e),
          'User-Agent': 'VSCode-AugmentTokenManager/1.0.0',
          'X-API-Key': _0x4242ad(0x241)
        },
        _0x4569cc = await fetch(_0x5be3c8[_0x4242ad(0x210)](), {
          'method': _0x4242ad(0x28b),
          'headers': _0x5cc6b1,
          'body': JSON[_0x4242ad(0x24c)](_0x1a2acb),
          'signal': AbortSignal['timeout'](0x7530)
        });
      if (!_0x4569cc['ok']) throw new Error('HTTP\x20' + _0x4569cc[_0x4242ad(0x25b)] + ':\x20' + _0x4569cc[_0x4242ad(0x1e9)]);
      const _0x15d9f7 = await _0x4569cc[_0x4242ad(0x248)]();
      return _0x15d9f7 && _0x15d9f7[_0x4242ad(0x1dd)] ? {
        'success': !![],
        'message': _0x15d9f7['message'],
        'user_ck': _0x15d9f7[_0x4242ad(0x234)]
      } : {
        'success': ![],
        'error': _0x15d9f7[_0x4242ad(0x26e)] || _0x4242ad(0x1ea)
      };
    } catch (_0x3df621) {
      return {
        'success': ![],
        'error': _0x3df621[_0x4242ad(0x26e)]
      };
    }
  }
  async [_0x592fd3(0x25e)]() {
    const _0x521a58 = _0x592fd3;
    try {
      this['sendMessage']({
        'command': 'loading',
        'data': {
          'message': 'Updating\x20machine\x20code...'
        }
      });
      const _0x4c2239 = require(_0x521a58(0x1f5)),
        _0x489a10 = _0x4c2239[_0x521a58(0x256)]();
      await this['context']['globalState']['update'](_0x521a58(0x265), _0x489a10), this[_0x521a58(0x1f8)]({
        'command': 'success',
        'data': {
          'message': _0x521a58(0x22b) + _0x489a10
        }
      });
    } catch (_0x2d16f6) {
      this[_0x521a58(0x1f8)]({
        'command': _0x521a58(0x1e2),
        'data': {
          'message': _0x521a58(0x231) + _0x2d16f6[_0x521a58(0x26e)]
        }
      });
    }
  } [_0x592fd3(0x1f8)](_0x4f1672) {
    const _0x4dad1e = _0x592fd3;
    this[_0x4dad1e(0x1f4)] && this['_view'][_0x4dad1e(0x23e)] && this[_0x4dad1e(0x1f4)]['webview'][_0x4dad1e(0x221)](_0x4f1672);
  } ['getWebviewContent']() {
    const _0x25b120 = _0x592fd3;
    return _0x25b120(0x263);
  }
}
class SimpleTokenManagerIntegration {
  constructor() {
    const _0x4afae5 = _0x592fd3;
    this['isInitialized'] = ![], this[_0x4afae5(0x1e8)] = null;
  }
  async [_0x592fd3(0x252)](_0x384f04) {
    const _0x1b3562 = _0x592fd3;
    if (this['isInitialized']) return;
    try {
      this[_0x1b3562(0x235)] = _0x384f04, this[_0x1b3562(0x1cf)] = new SimpleTokenManager(_0x384f04), this[_0x1b3562(0x1ce)] = new SimpleTokenApiService(), this['sidebarProvider'] = new SimpleTokenManagerSidebarProvider(_0x384f04, this[_0x1b3562(0x1cf)], this[_0x1b3562(0x1ce)]);
      const _0x8b66ec = vscode['window'][_0x1b3562(0x200)](_0x1b3562(0x238), this[_0x1b3562(0x1e8)], {
        'webviewOptions': {
          'retainContextWhenHidden': !![]
        }
      });
      _0x384f04[_0x1b3562(0x23d)]['push'](_0x8b66ec), this[_0x1b3562(0x1e3)] = !![];
    } catch (_0xe7bb0b) {
      throw _0xe7bb0b;
    }
  } [_0x592fd3(0x222)]() {
    const _0x52b946 = _0x592fd3;
    this[_0x52b946(0x1e3)] = ![];
  }
}
module[_0x592fd3(0x236)] = SimpleTokenManagerIntegration;