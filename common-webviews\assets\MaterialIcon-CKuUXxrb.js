import{A as w,C as t,X as o,$ as d,J as k,Q as A,ab as I,S as L,_ as r,m as n,R,W as j,t as x,b as S,I as y,Y as e,K as c}from"./SpinnerAugment-BY2Lraps.js";var C=k("<span> </span>");function F($,a){w(a,!1);let f=t(a,"class",8,""),g=t(a,"iconName",8,""),l=t(a,"fill",8,!1),m=t(a,"grade",8,"normal"),p=t(a,"title",24,()=>{}),b=n(),h=n(),i=n();o(()=>c(l()),()=>{e(b,l()?"1":"0")}),o(()=>c(l()),()=>{e(h,l()?"700":"400")}),o(()=>c(m()),()=>{switch(m()){case"low":e(i,"-25");break;case"normal":e(i,"0");break;case"high":e(i,"200")}}),d();var s=C(),v=x(s);A(()=>{I(s,1,`material-symbols-outlined ${f()}`,"svelte-htlsjs"),L(s,`font-variation-settings: 'FILL' ${r(b)??""}, 'wght' ${r(h)??""}, 'GRAD' ${r(i)??""};`),R(s,"title",p()),j(v,g())}),S($,s),y()}export{F as M};
