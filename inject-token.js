/**
 * Augment VSCode Plugin Token Injection Script
 * 
 * 这个脚本帮助你快速注入token到VSCode配置中进行测试
 * 使用方法: node inject-token.js
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 配置你的token和URL
const CONFIG = {
    // 在这里填入你的JWT token
    token: "YOUR_JWT_TOKEN_HERE",
    
    // 在这里填入你的tenant URL
    tenantUrl: "YOUR_TENANT_URL_HERE",
    
    // 示例:
    // token: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    // tenantUrl: "https://api.augmentcode.com"
};

/**
 * 获取VSCode用户设置文件路径
 */
function getVSCodeSettingsPath() {
    const platform = os.platform();
    let settingsPath;
    
    switch (platform) {
        case 'win32':
            settingsPath = path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'settings.json');
            break;
        case 'darwin':
            settingsPath = path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User', 'settings.json');
            break;
        case 'linux':
            settingsPath = path.join(os.homedir(), '.config', 'Code', 'User', 'settings.json');
            break;
        default:
            throw new Error(`Unsupported platform: ${platform}`);
    }
    
    return settingsPath;
}

/**
 * 读取现有的VSCode设置
 */
function readVSCodeSettings(settingsPath) {
    try {
        if (fs.existsSync(settingsPath)) {
            const content = fs.readFileSync(settingsPath, 'utf8');
            return JSON.parse(content);
        }
        return {};
    } catch (error) {
        console.warn('Warning: Could not parse existing settings.json, creating new one');
        return {};
    }
}

/**
 * 写入VSCode设置
 */
function writeVSCodeSettings(settingsPath, settings) {
    // 确保目录存在
    const dir = path.dirname(settingsPath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    
    // 写入设置文件
    fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8');
}

/**
 * 验证配置
 */
function validateConfig() {
    if (CONFIG.token === "YOUR_JWT_TOKEN_HERE") {
        throw new Error('请在脚本中设置你的JWT token');
    }
    
    if (CONFIG.tenantUrl === "YOUR_TENANT_URL_HERE") {
        throw new Error('请在脚本中设置你的tenant URL');
    }
    
    if (!CONFIG.tenantUrl.startsWith('https://')) {
        throw new Error('Tenant URL必须以https://开头');
    }
    
    // 简单验证JWT格式 (应该有两个点分隔三部分)
    if (CONFIG.token.split('.').length !== 3) {
        console.warn('Warning: Token格式可能不正确，JWT应该有三个部分用点分隔');
    }
}

/**
 * 注入token到VSCode配置
 */
function injectToken() {
    try {
        console.log('🚀 开始注入Augment token...');
        
        // 验证配置
        validateConfig();
        
        // 获取设置文件路径
        const settingsPath = getVSCodeSettingsPath();
        console.log(`📁 VSCode设置文件路径: ${settingsPath}`);
        
        // 读取现有设置
        const settings = readVSCodeSettings(settingsPath);
        console.log('📖 读取现有设置完成');
        
        // 备份现有设置
        const backupPath = settingsPath + '.backup.' + Date.now();
        if (fs.existsSync(settingsPath)) {
            fs.copyFileSync(settingsPath, backupPath);
            console.log(`💾 已备份现有设置到: ${backupPath}`);
        }
        
        // 注入Augment配置
        settings['augment.advanced.apiToken'] = CONFIG.token;
        settings['augment.advanced.completionURL'] = CONFIG.tenantUrl;
        
        // 确保禁用OAuth模式 (这样会优先使用配置的token)
        if (settings['augment.advanced.oauth']) {
            delete settings['augment.advanced.oauth'];
        }
        
        // 写入新设置
        writeVSCodeSettings(settingsPath, settings);
        
        console.log('✅ Token注入成功!');
        console.log(`🔑 API Token: ${CONFIG.token.substring(0, 20)}...`);
        console.log(`🌐 Tenant URL: ${CONFIG.tenantUrl}`);
        console.log('');
        console.log('📋 下一步:');
        console.log('1. 重启VSCode');
        console.log('2. 打开Augment聊天界面 (Ctrl+Shift+P -> "Augment: Open Chat")');
        console.log('3. 发送测试消息');
        console.log('4. 检查是否收到响应');
        
    } catch (error) {
        console.error('❌ 注入失败:', error.message);
        process.exit(1);
    }
}

/**
 * 清理注入的token
 */
function cleanToken() {
    try {
        console.log('🧹 清理Augment token...');
        
        const settingsPath = getVSCodeSettingsPath();
        const settings = readVSCodeSettings(settingsPath);
        
        // 删除Augment相关配置
        delete settings['augment.advanced.apiToken'];
        delete settings['augment.advanced.completionURL'];
        
        writeVSCodeSettings(settingsPath, settings);
        
        console.log('✅ Token清理完成!');
        console.log('请重启VSCode以使更改生效');
        
    } catch (error) {
        console.error('❌ 清理失败:', error.message);
        process.exit(1);
    }
}

/**
 * 显示当前配置
 */
function showConfig() {
    try {
        const settingsPath = getVSCodeSettingsPath();
        const settings = readVSCodeSettings(settingsPath);
        
        console.log('📋 当前Augment配置:');
        console.log(`API Token: ${settings['augment.advanced.apiToken'] || '未设置'}`);
        console.log(`Completion URL: ${settings['augment.advanced.completionURL'] || '未设置'}`);
        
    } catch (error) {
        console.error('❌ 读取配置失败:', error.message);
    }
}

// 主程序
function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    switch (command) {
        case 'inject':
        case undefined:
            injectToken();
            break;
        case 'clean':
            cleanToken();
            break;
        case 'show':
            showConfig();
            break;
        case 'help':
            console.log('使用方法:');
            console.log('  node inject-token.js [inject]  - 注入token (默认)');
            console.log('  node inject-token.js clean    - 清理token');
            console.log('  node inject-token.js show     - 显示当前配置');
            console.log('  node inject-token.js help     - 显示帮助');
            break;
        default:
            console.error(`未知命令: ${command}`);
            console.log('使用 "node inject-token.js help" 查看帮助');
            process.exit(1);
    }
}

// 运行主程序
if (require.main === module) {
    main();
}

module.exports = {
    injectToken,
    cleanToken,
    showConfig
};
