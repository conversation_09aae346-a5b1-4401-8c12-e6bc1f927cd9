import {
  ba as D,
  bb as Ht,
  aP as g,
  bc as x,
  bd as Jt,
  be as Tt,
  aO as Et,
  bf as Xt,
  bg as It,
  bh as R,
  aM as X,
  bi as Yt,
  bj as st,
  bk as Zt,
  bl as M,
  b8 as kt,
  bm as V,
  aK as Ut,
  bn as tn,
  bo as Y,
  bp as nn,
  bq as en,
  br as U,
  aS as rn,
  bs as an,
  aN as on,
  bt,
  bu as un,
  bv as cn,
  aR as fn,
  aQ as Bt,
  b6 as sn,
  bw as Q
} from "./AugmentMessage-FtcicXdY.js";
var bn = "[object Symbol]";

function tt(t) {
  return typeof t == "symbol" || D(t) && Ht(t) == bn
}

function Dt(t, n) {
  for (var e = -1, r = t == null ? 0 : t.length, o = Array(r); ++e < r;) o[e] = n(t[e], e, t);
  return o
}
var lt = x ? x.prototype : void 0,
  vt = lt ? lt.toString : void 0;

function Mt(t) {
  if (typeof t == "string") return t;
  if (g(t)) return Dt(t, Mt) + "";
  if (tt(t)) return vt ? vt.call(t) : "";
  var n = t + "";
  return n == "0" && 1 / t == -1 / 0 ? "-0" : n
}

function ln() { }

function zt(t, n) {
  for (var e = -1, r = t == null ? 0 : t.length; ++e < r && n(t[e], e, t) !== !1;);
  return t
}

function vn(t, n, e, r) {
  for (var o = t.length, a = e + -1; ++a < o;)
    if (n(t[a], a, t)) return a;
  return -1
}

function jn(t) {
  return t != t
}

function hn(t, n, e) {
  return n == n ? function (r, o, a) {
    for (var u = a - 1, c = r.length; ++u < c;)
      if (r[u] === o) return u;
    return -1
  }(t, n, e) : vn(t, jn, e)
}

function yn(t, n) {
  return !!(t != null && t.length) && hn(t, n, 0) > -1
}

function I(t) {
  return Et(t) ? Jt(t) : Tt(t)
}
var pn = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,
  gn = /^\w*$/;

function nt(t, n) {
  if (g(t)) return !1;
  var e = typeof t;
  return !(e != "number" && e != "symbol" && e != "boolean" && t != null && !tt(t)) || gn.test(t) || !pn.test(t) || n != null && t in Object(n)
}
var jt, W, H, dn = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,
  wn = /\\(\\)?/g,
  An = (jt = function (t) {
    var n = [];
    return t.charCodeAt(0) === 46 && n.push(""), t.replace(dn, function (e, r, o, a) {
      n.push(o ? a.replace(wn, "$1") : r || e)
    }), n
  }, W = Xt(jt, function (t) {
    return H.size === 500 && H.clear(), t
  }), H = W.cache, W);

function _n(t) {
  return t == null ? "" : Mt(t)
}

function Ft(t, n) {
  return g(t) ? t : nt(t, n) ? [t] : An(_n(t))
}

function q(t) {
  if (typeof t == "string" || tt(t)) return t;
  var n = t + "";
  return n == "0" && 1 / t == -1 / 0 ? "-0" : n
}

function Pt(t, n) {
  for (var e = 0, r = (n = Ft(n, t)).length; t != null && e < r;) t = t[q(n[e++])];
  return e && e == r ? t : void 0
}

function et(t, n) {
  for (var e = -1, r = n.length, o = t.length; ++e < r;) t[o + e] = n[e];
  return t
}
var ht = x ? x.isConcatSpreadable : void 0;

function mn(t) {
  return g(t) || It(t) || !!(ht && t && t[ht])
}

function ze(t, n, e, r, o) {
  var a = -1,
    u = t.length;
  for (e || (e = mn), o || (o = []); ++a < u;) {
    var c = t[a];
    e(c) ? et(o, c) : r || (o[o.length] = c)
  }
  return o
}

function On(t, n, e, r) {
  var o = -1,
    a = t == null ? 0 : t.length;
  for (r && a && (e = t[++o]); ++o < a;) e = n(e, t[o], o, t);
  return e
}

function Ct(t, n) {
  for (var e = -1, r = t == null ? 0 : t.length, o = 0, a = []; ++e < r;) {
    var u = t[e];
    n(u, e, t) && (a[o++] = u)
  }
  return a
}

function Lt() {
  return []
}
var Sn = Object.prototype.propertyIsEnumerable,
  yt = Object.getOwnPropertySymbols,
  rt = yt ? function (t) {
    return t == null ? [] : (t = Object(t), Ct(yt(t), function (n) {
      return Sn.call(t, n)
    }))
  } : Lt,
  Nt = Object.getOwnPropertySymbols ? function (t) {
    for (var n = []; t;) et(n, rt(t)), t = Yt(t);
    return n
  } : Lt;

function Rt(t, n, e) {
  var r = n(t);
  return g(t) ? r : et(r, e(t))
}

function Z(t) {
  return Rt(t, I, rt)
}

function xn(t) {
  return Rt(t, X, Nt)
}
var En = Object.prototype.hasOwnProperty,
  In = /\w*$/,
  pt = x ? x.prototype : void 0,
  gt = pt ? pt.valueOf : void 0,
  kn = "[object Boolean]",
  Un = "[object Date]",
  Bn = "[object Map]",
  Dn = "[object Number]",
  Mn = "[object RegExp]",
  zn = "[object Set]",
  Fn = "[object String]",
  Pn = "[object Symbol]",
  Cn = "[object ArrayBuffer]",
  Ln = "[object DataView]",
  Nn = "[object Float32Array]",
  Rn = "[object Float64Array]",
  $n = "[object Int8Array]",
  Vn = "[object Int16Array]",
  qn = "[object Int32Array]",
  Gn = "[object Uint8Array]",
  Kn = "[object Uint8ClampedArray]",
  Qn = "[object Uint16Array]",
  Wn = "[object Uint32Array]";

function Hn(t, n, e) {
  var r, o = t.constructor;
  switch (n) {
    case Cn:
      return st(t);
    case kn:
    case Un:
      return new o(+t);
    case Ln:
      return function (a, u) {
        var c = u ? st(a.buffer) : a.buffer;
        return new a.constructor(c, a.byteOffset, a.byteLength)
      }(t, e);
    case Nn:
    case Rn:
    case $n:
    case Vn:
    case qn:
    case Gn:
    case Kn:
    case Qn:
    case Wn:
      return Zt(t, e);
    case Bn:
      return new o;
    case Dn:
    case Fn:
      return new o(t);
    case Mn:
      return function (a) {
        var u = new a.constructor(a.source, In.exec(a));
        return u.lastIndex = a.lastIndex, u
      }(t);
    case zn:
      return new o;
    case Pn:
      return r = t, gt ? Object(gt.call(r)) : {}
  }
}
var dt = V && V.isMap,
  Jn = dt ? kt(dt) : function (t) {
    return D(t) && M(t) == "[object Map]"
  },
  wt = V && V.isSet,
  Tn = wt ? kt(wt) : function (t) {
    return D(t) && M(t) == "[object Set]"
  },
  $t = "[object Arguments]",
  Vt = "[object Function]",
  qt = "[object Object]",
  b = {};

function J(t, n, e, r, o, a) {
  var u, c = 1 & n,
    l = 2 & n,
    h = 4 & n;
  if (u !== void 0) return u;
  if (!Ut(t)) return t;
  var y = g(t);
  if (y) {
    if (u = function (f) {
      var j = f.length,
        i = new f.constructor(j);
      return j && typeof f[0] == "string" && En.call(f, "index") && (i.index = f.index, i.input = f.input), i
    }(t), !c) return tn(t, u)
  } else {
    var v = M(t),
      p = v == Vt || v == "[object GeneratorFunction]";
    if (Y(t)) return nn(t, c);
    if (v == qt || v == $t || p && !o) {
      if (u = l || p ? {} : en(t), !c) return l ? function (f, j) {
        return R(f, Nt(f), j)
      }(t, function (f, j) {
        return f && R(j, X(j), f)
      }(u, t)) : function (f, j) {
        return R(f, rt(f), j)
      }(t, function (f, j) {
        return f && R(j, I(j), f)
      }(u, t))
    } else {
      if (!b[v]) return o ? t : {};
      u = Hn(t, v, c)
    }
  }
  a || (a = new U);
  var A = a.get(t);
  if (A) return A;
  a.set(t, u), Tn(t) ? t.forEach(function (f) {
    u.add(J(f, n, e, f, t, a))
  }) : Jn(t) && t.forEach(function (f, j) {
    u.set(j, J(f, n, e, j, t, a))
  });
  var d = y ? void 0 : (h ? l ? xn : Z : l ? X : I)(t);
  return zt(d || t, function (f, j) {
    d && (f = t[j = f]), rn(u, j, J(f, n, e, j, t, a))
  }), u
}
b[$t] = b["[object Array]"] = b["[object ArrayBuffer]"] = b["[object DataView]"] = b["[object Boolean]"] = b["[object Date]"] = b["[object Float32Array]"] = b["[object Float64Array]"] = b["[object Int8Array]"] = b["[object Int16Array]"] = b["[object Int32Array]"] = b["[object Map]"] = b["[object Number]"] = b[qt] = b["[object RegExp]"] = b["[object Set]"] = b["[object String]"] = b["[object Symbol]"] = b["[object Uint8Array]"] = b["[object Uint8ClampedArray]"] = b["[object Uint16Array]"] = b["[object Uint32Array]"] = !0, b["[object Error]"] = b[Vt] = b["[object WeakMap]"] = !1;

function B(t) {
  var n = -1,
    e = t == null ? 0 : t.length;
  for (this.__data__ = new an; ++n < e;) this.add(t[n])
}

function Xn(t, n) {
  for (var e = -1, r = t == null ? 0 : t.length; ++e < r;)
    if (n(t[e], e, t)) return !0;
  return !1
}

function Gt(t, n) {
  return t.has(n)
}
B.prototype.add = B.prototype.push = function (t) {
  return this.__data__.set(t, "__lodash_hash_undefined__"), this
}, B.prototype.has = function (t) {
  return this.__data__.has(t)
};
var Yn = 1,
  Zn = 2;

function At(t, n, e, r, o, a) {
  var u = e & Yn,
    c = t.length,
    l = n.length;
  if (c != l && !(u && l > c)) return !1;
  var h = a.get(t),
    y = a.get(n);
  if (h && y) return h == n && y == t;
  var v = -1,
    p = !0,
    A = e & Zn ? new B : void 0;
  for (a.set(t, n), a.set(n, t); ++v < c;) {
    var d = t[v],
      f = n[v];
    if (r) var j = u ? r(f, d, v, n, t, a) : r(d, f, v, t, n, a);
    if (j !== void 0) {
      if (j) continue;
      p = !1;
      break
    }
    if (A) {
      if (!Xn(n, function (i, s) {
        if (!Gt(A, s) && (d === i || o(d, i, e, r, a))) return A.push(s)
      })) {
        p = !1;
        break
      }
    } else if (d !== f && !o(d, f, e, r, a)) {
      p = !1;
      break
    }
  }
  return a.delete(t), a.delete(n), p
}

function te(t) {
  var n = -1,
    e = Array(t.size);
  return t.forEach(function (r, o) {
    e[++n] = [o, r]
  }), e
}

function at(t) {
  var n = -1,
    e = Array(t.size);
  return t.forEach(function (r) {
    e[++n] = r
  }), e
}
var ne = 1,
  ee = 2,
  re = "[object Boolean]",
  ae = "[object Date]",
  oe = "[object Error]",
  ue = "[object Map]",
  ce = "[object Number]",
  ie = "[object RegExp]",
  fe = "[object Set]",
  se = "[object String]",
  be = "[object Symbol]",
  le = "[object ArrayBuffer]",
  ve = "[object DataView]",
  _t = x ? x.prototype : void 0,
  T = _t ? _t.valueOf : void 0,
  je = 1,
  he = Object.prototype.hasOwnProperty,
  ye = 1,
  mt = "[object Arguments]",
  Ot = "[object Array]",
  $ = "[object Object]",
  St = Object.prototype.hasOwnProperty;

function pe(t, n, e, r, o, a) {
  var u = g(t),
    c = g(n),
    l = u ? Ot : M(t),
    h = c ? Ot : M(n),
    y = (l = l == mt ? $ : l) == $,
    v = (h = h == mt ? $ : h) == $,
    p = l == h;
  if (p && Y(t)) {
    if (!Y(n)) return !1;
    u = !0, y = !1
  }
  if (p && !y) return a || (a = new U), u || un(t) ? At(t, n, e, r, o, a) : function (i, s, z, O, G, w, _) {
    switch (z) {
      case ve:
        if (i.byteLength != s.byteLength || i.byteOffset != s.byteOffset) return !1;
        i = i.buffer, s = s.buffer;
      case le:
        return !(i.byteLength != s.byteLength || !w(new bt(i), new bt(s)));
      case re:
      case ae:
      case ce:
        return on(+i, +s);
      case oe:
        return i.name == s.name && i.message == s.message;
      case ie:
      case se:
        return i == s + "";
      case ue:
        var S = te;
      case fe:
        var k = O & ne;
        if (S || (S = at), i.size != s.size && !k) return !1;
        var E = _.get(i);
        if (E) return E == s;
        O |= ee, _.set(i, s);
        var m = At(S(i), S(s), O, G, w, _);
        return _.delete(i), m;
      case be:
        if (T) return T.call(i) == T.call(s)
    }
    return !1
  }(t, n, l, e, r, o, a);
  if (!(e & ye)) {
    var A = y && St.call(t, "__wrapped__"),
      d = v && St.call(n, "__wrapped__");
    if (A || d) {
      var f = A ? t.value() : t,
        j = d ? n.value() : n;
      return a || (a = new U), o(f, j, e, r, a)
    }
  }
  return !!p && (a || (a = new U), function (i, s, z, O, G, w) {
    var _ = z & je,
      S = Z(i),
      k = S.length;
    if (k != Z(s).length && !_) return !1;
    for (var E = k; E--;) {
      var m = S[E];
      if (!(_ ? m in s : he.call(s, m))) return !1
    }
    var ct = w.get(i),
      it = w.get(s);
    if (ct && it) return ct == s && it == i;
    var F = !0;
    w.set(i, s), w.set(s, i);
    for (var K = _; ++E < k;) {
      var P = i[m = S[E]],
        C = s[m];
      if (O) var ft = _ ? O(C, P, m, s, i, w) : O(P, C, m, i, s, w);
      if (!(ft === void 0 ? P === C || G(P, C, z, O, w) : ft)) {
        F = !1;
        break
      }
      K || (K = m == "constructor")
    }
    if (F && !K) {
      var L = i.constructor,
        N = s.constructor;
      L == N || !("constructor" in i) || !("constructor" in s) || typeof L == "function" && L instanceof L && typeof N == "function" && N instanceof N || (F = !1)
    }
    return w.delete(i), w.delete(s), F
  }(t, n, e, r, o, a))
}

function ot(t, n, e, r, o) {
  return t === n || (t == null || n == null || !D(t) && !D(n) ? t != t && n != n : pe(t, n, e, r, ot, o))
}
var ge = 1,
  de = 2;

function Kt(t) {
  return t == t && !Ut(t)
}

function Qt(t, n) {
  return function (e) {
    return e != null && e[t] === n && (n !== void 0 || t in Object(e))
  }
}

function we(t) {
  var n = function (e) {
    for (var r = I(e), o = r.length; o--;) {
      var a = r[o],
        u = e[a];
      r[o] = [a, u, Kt(u)]
    }
    return r
  }(t);
  return n.length == 1 && n[0][2] ? Qt(n[0][0], n[0][1]) : function (e) {
    return e === t || function (r, o, a, u) {
      var c = a.length,
        l = c;
      if (r == null) return !l;
      for (r = Object(r); c--;) {
        var h = a[c];
        if (h[2] ? h[1] !== r[h[0]] : !(h[0] in r)) return !1
      }
      for (; ++c < l;) {
        var y = (h = a[c])[0],
          v = r[y],
          p = h[1];
        if (h[2]) {
          if (v === void 0 && !(y in r)) return !1
        } else {
          var A = new U;
          if (!ot(p, v, ge | de, u, A)) return !1
        }
      }
      return !0
    }(e, 0, n)
  }
}

function Ae(t, n) {
  return t != null && n in Object(t)
}

function _e(t, n, e) {
  for (var r = -1, o = (n = Ft(n, t)).length, a = !1; ++r < o;) {
    var u = q(n[r]);
    if (!(a = t != null && e(t, u))) break;
    t = t[u]
  }
  return a || ++r != o ? a : !!(o = t == null ? 0 : t.length) && cn(o) && fn(u, o) && (g(t) || It(t))
}

function me(t, n) {
  return t != null && _e(t, n, Ae)
}
var Oe = 1,
  Se = 2;

function xe(t, n) {
  return nt(t) && Kt(n) ? Qt(q(t), n) : function (e) {
    var r = function (o, a, u) {
      var c = o == null ? void 0 : Pt(o, a);
      return c === void 0 ? u : c
    }(e, t);
    return r === void 0 && r === n ? me(e, t) : ot(n, r, Oe | Se)
  }
}

function Ee(t) {
  return nt(t) ? (n = q(t), function (e) {
    return e == null ? void 0 : e[n]
  }) : function (e) {
    return function (r) {
      return Pt(r, e)
    }
  }(t);
  var n
}

function Wt(t) {
  return typeof t == "function" ? t : t == null ? Bt : typeof t == "object" ? g(t) ? xe(t[0], t[1]) : we(t) : Ee(t)
}

function Ie(t, n) {
  return t && sn(t, n, I)
}
var xt, ut = (xt = Ie, function (t, n) {
  if (t == null) return t;
  if (!Et(t)) return xt(t, n);
  for (var e = t.length, r = -1, o = Object(t); ++r < e && n(o[r], r, o) !== !1;);
  return t
});

function ke(t) {
  return typeof t == "function" ? t : Bt
}

function Fe(t, n) {
  return (g(t) ? zt : ut)(t, ke(n))
}

function Ue(t, n) {
  var e = [];
  return ut(t, function (r, o, a) {
    n(r, o, a) && e.push(r)
  }), e
}

function Pe(t, n) {
  return (g(t) ? Ct : Ue)(t, Wt(n))
}

function Ce(t) {
  return t == null ? [] : function (n, e) {
    return Dt(e, function (r) {
      return n[r]
    })
  }(t, I(t))
}

function Le(t) {
  return t === void 0
}

function Be(t, n, e, r, o) {
  return o(t, function (a, u, c) {
    e = r ? (r = !1, a) : n(e, a, u, c)
  }), e
}

function Ne(t, n, e) {
  var r = g(t) ? On : Be,
    o = arguments.length < 3;
  return r(t, Wt(n), e, o, ut)
}
var De = Q && 1 / at(new Q([, -0]))[1] == 1 / 0 ? function (t) {
  return new Q(t)
} : ln;

function Re(t, n, e) {
  var r = -1,
    o = yn,
    a = t.length,
    u = !0,
    c = [],
    l = c;
  if (a >= 200) {
    var h = n ? null : De(t);
    if (h) return at(h);
    u = !1, o = Gt, l = new B
  } else l = n ? [] : c;
  t: for (; ++r < a;) {
    var y = t[r],
      v = n ? n(y) : y;
    if (y = y !== 0 ? y : 0, u && v == v) {
      for (var p = l.length; p--;)
        if (l[p] === v) continue t;
      n && l.push(v), c.push(y)
    } else o(l, v, e) || (l !== c && l.push(v), c.push(y))
  }
  return c
}
export {
  Ct as A, Ue as B, Xn as C, ln as D, B as S, Re as a, J as b, ze as c, Fe as d, tt as e, Pe as f, Wt as g, vn as h, Le as i, ut as j, I as k, Dt as l, _e as m, Ft as n, Pt as o, ke as p, Ie as q, Ne as r, me as s, q as t, _n as u, Ce as v, yn as w, Gt as x, hn as y, xn as z
};