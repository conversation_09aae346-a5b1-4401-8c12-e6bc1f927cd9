import{A as R,C as o,m as u,ak as ee,_ as e,Y as i,X,K as w,$ as ae,D as ie,J as Y,P as k,al as L,S as N,t as p,N as O,am as P,M as I,H as te,Q as se,a3 as re,ab as Q,b as T,I as de}from"./SpinnerAugment-BY2Lraps.js";import{a as ne,I as oe}from"./IconButtonAugment-B8y0FMb_.js";import{t as le,f as ce}from"./index-ALhsmmIa.js";import{E as ve}from"./ellipsis-BVNflcFA.js";const S=(_,{onResize:t,options:g})=>{const v=new ResizeObserver(t);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var me=Y('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=Y('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ge(_,t){R(t,!1);let g,v,B=o(t,"initialWidth",8,300),z=o(t,"expandedMinWidth",8,50),C=o(t,"minimizedWidth",8,0),s=o(t,"minimized",12,!1),j=o(t,"class",8,""),q=o(t,"showButton",8,!0),F=o(t,"deadzone",8,0),G=o(t,"columnLayoutThreshold",8,600),d=o(t,"layoutMode",28,()=>{}),$=u(),f=u(),l=u(!1),c=u(B()),y=u(B()),r=u(!1);function E(){s(!s())}function M(){if(e(f)){if(d()!==void 0)return i(r,d()==="column"),void(e(r)&&i(l,!1));i(r,e(f).clientWidth<G()),e(r)&&i(l,!1)}}ee(M),X(()=>(w(s()),w(d())),()=>{s()?(d("row"),i(r,!1)):d()!=="row"||s()||(d(void 0),M())}),X(()=>(w(d()),e(r)),()=>{d()!==void 0&&(i(r,d()==="column"),e(r)&&i(l,!1))}),X(()=>(w(s()),w(C()),e(c)),()=>{i(y,s()?C():e(c))}),ae(),ie();var h=ue();let H;k("mousemove",L,function(a){if(!e(l)||!e($)||e(r))return;const n=a.clientX-g,W=e(f).clientWidth-200,m=v+n;m<z()?m<z()-F()?s(!0):(i(c,z()),s(!1)):m>W?(i(c,W),s(!1)):(i(c,m),s(!1))}),k("mouseup",L,function(){i(l,!1),i(c,Math.max(e(c),z()))});var b=p(h),D=p(b);N(D,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var U=p(D);O(U,t,"left",{},null),P(b,a=>i($,a),()=>e($));var x=I(b,2);let J;var K=I(x,2),V=p(K);O(V,t,"right",{},null);var Z=I(K,2),A=a=>{var n=me(),W=p(n);oe(W,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:E},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,n,()=>ce,()=>({y:0,x:0,duration:200})),T(a,n)};te(Z,a=>{s()&&q()&&a(A)}),P(h,a=>i(f,a),()=>e(f)),ne(h,(a,n)=>S==null?void 0:S(a,n),()=>({onResize:()=>d()===void 0&&M()})),se((a,n)=>{H=Q(h,1,`c-drawer ${j()??""}`,"svelte-18f0m3o",H,a),N(b,`--augment-drawer-width:${e(y)??""}px;`),D.inert=e(l),J=Q(x,1,"c-drawer__handle svelte-18f0m3o",null,J,n)},[()=>({"is-dragging":e(l),"is-hidden":!e(y),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),k("mousedown",x,function(a){e(r)||(i(l,!0),g=a.clientX,v=e($).offsetWidth,a.preventDefault())}),k("dblclick",x,E),T(_,h),de()}export{ge as D,S as r};
