# Augment VSCode 插件 Token 注入测试指南

## 🎯 目标
测试是否可以通过注入token和tenant URL来绕过正常的OAuth登录流程

## 🚀 最简单的注入方法 - 配置文件注入

### 方法1: 通过VSCode设置界面 (推荐)

1. **打开VSCode设置**
   - 按 `Ctrl+,` (Windows/Linux) 或 `Cmd+,` (Mac)
   - 或者 File → Preferences → Settings

2. **搜索Augment设置**
   - 在搜索框输入: `augment.advanced`

3. **设置API Token**
   - 找到 `Augment › Advanced: Api Token`
   - 输入你的JWT token

4. **设置服务器URL**
   - 找到 `Augment › Advanced: Completion URL`
   - 输入你的tenant URL (例如: `https://api.augmentcode.com`)

### 方法2: 直接编辑settings.json

1. **打开settings.json**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入: `Preferences: Open Settings (JSON)`

2. **添加配置**
   ```json
   {
     "augment.advanced.apiToken": "YOUR_JWT_TOKEN_HERE",
     "augment.advanced.completionURL": "YOUR_TENANT_URL_HERE"
   }
   ```

### 方法3: 通过命令行 (如果你有VSCode CLI)

```bash
# 设置API Token
code --user-data-dir /path/to/vscode/data --install-extension augment.vscode-augment
code --user-data-dir /path/to/vscode/data --add "augment.advanced.apiToken=YOUR_TOKEN"
code --user-data-dir /path/to/vscode/data --add "augment.advanced.completionURL=YOUR_URL"
```

## 🧪 测试步骤

### 1. 准备测试环境

1. **确保插件已安装**
   - 检查VSCode扩展列表中是否有Augment插件
   - 版本应该是 0.524.1

2. **重启VSCode**
   - 设置token后重启VSCode让配置生效

### 2. 验证注入是否成功

1. **检查插件状态**
   - 看VSCode状态栏是否显示Augment相关状态
   - 正常情况下应该显示已连接状态

2. **打开Augment面板**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入: `Augment: Open Chat`
   - 看是否能正常打开聊天界面

3. **测试发送消息**
   - 在聊天界面输入简单消息
   - 点击 "Send to Agent" 或 "Send message"
   - 观察是否有响应

### 3. 监控网络请求 (可选)

1. **打开开发者工具**
   - 按 `F12` 或 `Ctrl+Shift+I`
   - 切换到 Network 标签

2. **发送测试消息**
   - 在Augment聊天界面发送消息
   - 观察Network标签中的HTTP请求

3. **检查请求头**
   - 查看是否有 `Authorization: Bearer YOUR_TOKEN`
   - 确认请求发送到正确的tenant URL

## 🔍 故障排除

### 常见问题

1. **插件显示未登录**
   - 检查token格式是否正确 (应该是JWT格式)
   - 检查tenant URL是否正确 (必须以https://开头)
   - 重启VSCode

2. **请求失败**
   - 检查token是否过期
   - 检查网络连接
   - 查看VSCode开发者控制台的错误信息

3. **配置不生效**
   - 确保配置保存后重启了VSCode
   - 检查settings.json语法是否正确
   - 确认没有其他OAuth配置冲突

### 调试方法

1. **查看VSCode日志**
   - Help → Toggle Developer Tools
   - 查看Console标签的错误信息

2. **检查配置是否生效**
   - 在开发者控制台输入:
   ```javascript
   vscode.workspace.getConfiguration('augment.advanced').get('apiToken')
   ```

## 📝 测试记录模板

```
测试日期: ___________
VSCode版本: ___________
Augment插件版本: 0.524.1

Token信息:
- Token长度: _____ 字符
- Tenant URL: _______________

测试结果:
□ 配置设置成功
□ VSCode重启后插件状态正常
□ 能够打开Augment聊天界面
□ 能够发送消息
□ 收到服务器响应
□ 网络请求包含正确的Authorization头

问题记录:
_________________________________
_________________________________

成功/失败: ___________
```

## 🎉 成功标志

如果以下都正常，说明token注入成功：

1. ✅ VSCode状态栏显示Augment已连接
2. ✅ 能够打开Augment聊天界面
3. ✅ 发送消息后收到响应
4. ✅ 网络请求显示正确的Authorization头
5. ✅ 请求发送到正确的tenant URL

## ⚠️ 注意事项

1. **安全性**: 这只是测试方法，实际使用时要保护好token
2. **有效期**: JWT token通常有过期时间，过期后需要重新获取
3. **权限**: 确保token有足够的权限访问所需的API端点
4. **网络**: 确保能够访问tenant URL指定的服务器
